// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		00E356F31AD99517003FC87E /* AssetifyAppTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* AssetifyAppTests.m */; };
		020A4F55147E1876288E0BDD /* BuildFile in Frameworks */ = {isa = PBXBuildFile; };
		13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		1402DCB32D2BF57C0041F37C /* NativeSampleModule.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1402DCB22D2BF57C0041F37C /* NativeSampleModule.cpp */; };
		1402DCB82D2BFCBB0041F37C /* MPCBindings.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1402DCB72D2BFCBB0041F37C /* MPCBindings.xcframework */; };
		14A47EE02D413D9F00A506E9 /* WalletManagerBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = FD679AEB2BBDA5C400961DEA /* WalletManagerBridge.m */; };
		14A47EE12D413D9F00A506E9 /* WalletManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = FD4DD9C22BBD868C00B1C105 /* WalletManager.swift */; };
		14A47EE22D413D9F00A506E9 /* WalletManagerBridge.swift in Sources */ = {isa = PBXBuildFile; fileRef = FD679AE92BBDA3B700961DEA /* WalletManagerBridge.swift */; };
		14A47EE32D413D9F00A506E9 /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		14A47EE42D413D9F00A506E9 /* NativeSampleModule.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 1402DCB22D2BF57C0041F37C /* NativeSampleModule.cpp */; };
		14A47EE52D413D9F00A506E9 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		14A47EE72D413D9F00A506E9 /* MPCBindings.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1402DCB72D2BFCBB0041F37C /* MPCBindings.xcframework */; };
		14A47EE82D413D9F00A506E9 /* WalletCore in Frameworks */ = {isa = PBXBuildFile; productRef = 14A47EDD2D413D9F00A506E9 /* WalletCore */; };
		14A47EE92D413D9F00A506E9 /* SwiftProtobuf in Frameworks */ = {isa = PBXBuildFile; productRef = 14A47EDB2D413D9F00A506E9 /* SwiftProtobuf */; };
		14A47EEC2D413D9F00A506E9 /* assetifyWithText.svg in Resources */ = {isa = PBXBuildFile; fileRef = FD1CBD112C06098500CBB41F /* assetifyWithText.svg */; };
		14A47EED2D413D9F00A506E9 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		14A47EEE2D413D9F00A506E9 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		14A47EEF2D413D9F00A506E9 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = FD5F5E5A2BB3159000CAC9C6 /* PrivacyInfo.xcprivacy */; };
		14A47EF02D413D9F00A506E9 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 8B4A6D182B060D5300599D81 /* GoogleService-Info.plist */; };
		14A47EF12D413D9F00A506E9 /* Poppins-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D2305C5EA91944C4BBFE6CEF /* Poppins-Light.ttf */; };
		14A47EF22D413D9F00A506E9 /* SF-Pro.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8928AE5ED0174BC08F0B21CE /* SF-Pro.ttf */; };
		5B141A2998CA404D39A3F9A5 /* libPods-AssetifyApp.a in Frameworks */ = {isa = PBXBuildFile; fileRef = DB3C728ADAB11E94C309257D /* libPods-AssetifyApp.a */; };
		62C92D7200593B0E3F37DC4C /* libPods-AssetifyLoans.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 4C254F4F0E13978373DE075C /* libPods-AssetifyLoans.a */; };
		81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		8B4A6D192B060D5300599D81 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 8B4A6D182B060D5300599D81 /* GoogleService-Info.plist */; };
		916107B39E5B49168F12660C /* SF-Pro.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8928AE5ED0174BC08F0B21CE /* SF-Pro.ttf */; };
		99F0D6ED2B1D4FB3BFFC80EF /* Poppins-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D2305C5EA91944C4BBFE6CEF /* Poppins-Light.ttf */; };
		E4A77B4A2DCCC504009CAAB4 /* TransactionManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4A77B492DCCC500009CAAB4 /* TransactionManager.swift */; };
		E4A77B4B2DCCC504009CAAB4 /* TransactionManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4A77B492DCCC500009CAAB4 /* TransactionManager.swift */; };
		FD1CBD122C06098500CBB41F /* assetifyWithText.svg in Resources */ = {isa = PBXBuildFile; fileRef = FD1CBD112C06098500CBB41F /* assetifyWithText.svg */; };
		FD36FFC92BBED859004C827C /* SwiftProtobuf in Frameworks */ = {isa = PBXBuildFile; productRef = FD36FFC82BBED859004C827C /* SwiftProtobuf */; };
		FD36FFCB2BBED859004C827C /* WalletCore in Frameworks */ = {isa = PBXBuildFile; productRef = FD36FFCA2BBED859004C827C /* WalletCore */; };
		FD4DD9C32BBD868C00B1C105 /* WalletManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = FD4DD9C22BBD868C00B1C105 /* WalletManager.swift */; };
		FD5F5E5B2BB3159000CAC9C6 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = FD5F5E5A2BB3159000CAC9C6 /* PrivacyInfo.xcprivacy */; };
		FD679AEA2BBDA3B700961DEA /* WalletManagerBridge.swift in Sources */ = {isa = PBXBuildFile; fileRef = FD679AE92BBDA3B700961DEA /* WalletManagerBridge.swift */; };
		FD679AEC2BBDA5C400961DEA /* WalletManagerBridge.m in Sources */ = {isa = PBXBuildFile; fileRef = FD679AEB2BBDA5C400961DEA /* WalletManagerBridge.m */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = AssetifyApp;
		};
		8B02E9AC2AE673A200CEF93F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 8B02E9A82AE673A200CEF93F /* RNInAppBrowser.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 134814201AA4EA6300B7C361;
			remoteInfo = RNInAppBrowser;
		};
		8B1980ED2AE7B44A00CA05B3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 8B1980E82AE7B44700CA05B3 /* RNColorThief.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 134814201AA4EA6300B7C361;
			remoteInfo = RNColorThief;
		};
		8B1980EF2AE7B44A00CA05B3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 8B1980E82AE7B44700CA05B3 /* RNColorThief.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = AA7BD0F4233C711300111F1B;
			remoteInfo = "RNColorThief-tvOS";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		14A47EF42D413D9F00A506E9 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
		8B83E08E2B88DD4F00D5D9E5 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		00E356EE1AD99517003FC87E /* AssetifyAppTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = AssetifyAppTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* AssetifyAppTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AssetifyAppTests.m; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* AssetifyApp.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = AssetifyApp.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = AssetifyApp/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = AppDelegate.mm; path = AssetifyApp/AppDelegate.mm; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = AssetifyApp/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = AssetifyApp/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = AssetifyApp/main.m; sourceTree = "<group>"; };
		1402DCB22D2BF57C0041F37C /* NativeSampleModule.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; path = NativeSampleModule.cpp; sourceTree = "<group>"; };
		1402DCB52D2BF5A70041F37C /* NativeSampleModule.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = NativeSampleModule.h; sourceTree = "<group>"; };
		1402DCB62D2BF5C80041F37C /* libmpc_core_bindings.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = libmpc_core_bindings.h; sourceTree = "<group>"; };
		1402DCB72D2BFCBB0041F37C /* MPCBindings.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = MPCBindings.xcframework; sourceTree = "<group>"; };
		140C6C3F2D3FC8D6001E0162 /* AssetifyLoans.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = AssetifyLoans.plist; path = "/Users/<USER>/Desktop/assetify/react-native-mpc-app/ios/AssetifyLoans.plist"; sourceTree = "<absolute>"; };
		14A47EFC2D413D9F00A506E9 /* AssetifyLoans.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = AssetifyLoans.app; sourceTree = BUILT_PRODUCTS_DIR; };
		14A47EFD2D413D9F00A506E9 /* AssetifyApp copy-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "AssetifyApp copy-Info.plist"; path = "/Users/<USER>/Desktop/assetify/react-native-mpc-app/ios/AssetifyApp copy-Info.plist"; sourceTree = "<absolute>"; };
		14AFBC381DE0A4AA75C2B415 /* Pods-AssetifyLoans.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-AssetifyLoans.debug.xcconfig"; path = "Target Support Files/Pods-AssetifyLoans/Pods-AssetifyLoans.debug.xcconfig"; sourceTree = "<group>"; };
		4C254F4F0E13978373DE075C /* libPods-AssetifyLoans.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-AssetifyLoans.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = AssetifyApp/LaunchScreen.storyboard; sourceTree = "<group>"; };
		8928AE5ED0174BC08F0B21CE /* SF-Pro.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "SF-Pro.ttf"; path = "../src/assets/fonts/SF-Pro.ttf"; sourceTree = "<group>"; };
		8B02E9A82AE673A200CEF93F /* RNInAppBrowser.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RNInAppBrowser.xcodeproj; path = "../node_modules/react-native-inappbrowser-reborn/ios/RNInAppBrowser.xcodeproj"; sourceTree = "<group>"; };
		8B1980E82AE7B44700CA05B3 /* RNColorThief.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RNColorThief.xcodeproj; path = "../node_modules/react-native-color-thief/ios/RNColorThief.xcodeproj"; sourceTree = "<group>"; };
		8B4A6D182B060D5300599D81 /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = "GoogleService-Info.plist"; path = "AssetifyApp/GoogleService-Info.plist"; sourceTree = "<group>"; };
		8B570DEB2B876FE900B19C0B /* AssetifyApp.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = AssetifyApp.entitlements; path = AssetifyApp/AssetifyApp.entitlements; sourceTree = "<group>"; };
		C61BADD5FB8908CE852A7AD2 /* Pods-AssetifyLoans.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-AssetifyLoans.release.xcconfig"; path = "Target Support Files/Pods-AssetifyLoans/Pods-AssetifyLoans.release.xcconfig"; sourceTree = "<group>"; };
		CCD1FB6351F5AE779B2A07AF /* Pods-AssetifyApp.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-AssetifyApp.release.xcconfig"; path = "Target Support Files/Pods-AssetifyApp/Pods-AssetifyApp.release.xcconfig"; sourceTree = "<group>"; };
		D2305C5EA91944C4BBFE6CEF /* Poppins-Light.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "Poppins-Light.ttf"; path = "../src/assets/fonts/Poppins-Light.ttf"; sourceTree = "<group>"; };
		DB3C728ADAB11E94C309257D /* libPods-AssetifyApp.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-AssetifyApp.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		E4A77B492DCCC500009CAAB4 /* TransactionManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TransactionManager.swift; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		F296B38F2EEE2EE7447E78D5 /* Pods-AssetifyApp.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-AssetifyApp.debug.xcconfig"; path = "Target Support Files/Pods-AssetifyApp/Pods-AssetifyApp.debug.xcconfig"; sourceTree = "<group>"; };
		FD1CBD112C06098500CBB41F /* assetifyWithText.svg */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; name = assetifyWithText.svg; path = ../src/assets/logo/assetifyWithText.svg; sourceTree = "<group>"; };
		FD4DD9C22BBD868C00B1C105 /* WalletManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WalletManager.swift; sourceTree = "<group>"; };
		FD4DD9C72BBD8ED300B1C105 /* AssetifyApp-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "AssetifyApp-Bridging-Header.h"; sourceTree = "<group>"; };
		FD5F5E5A2BB3159000CAC9C6 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		FD679AE92BBDA3B700961DEA /* WalletManagerBridge.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WalletManagerBridge.swift; sourceTree = "<group>"; };
		FD679AEB2BBDA5C400961DEA /* WalletManagerBridge.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WalletManagerBridge.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		00E356EB1AD99517003FC87E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				1402DCB82D2BFCBB0041F37C /* MPCBindings.xcframework in Frameworks */,
				FD36FFCB2BBED859004C827C /* WalletCore in Frameworks */,
				FD36FFC92BBED859004C827C /* SwiftProtobuf in Frameworks */,
				020A4F55147E1876288E0BDD /* BuildFile in Frameworks */,
				5B141A2998CA404D39A3F9A5 /* libPods-AssetifyApp.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		14A47EE62D413D9F00A506E9 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				14A47EE72D413D9F00A506E9 /* MPCBindings.xcframework in Frameworks */,
				14A47EE82D413D9F00A506E9 /* WalletCore in Frameworks */,
				14A47EE92D413D9F00A506E9 /* SwiftProtobuf in Frameworks */,
				62C92D7200593B0E3F37DC4C /* libPods-AssetifyLoans.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* AssetifyAppTests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* AssetifyAppTests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = AssetifyAppTests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* AssetifyApp */ = {
			isa = PBXGroup;
			children = (
				E4A77B492DCCC500009CAAB4 /* TransactionManager.swift */,
				8B570DEB2B876FE900B19C0B /* AssetifyApp.entitlements */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				FD679AEB2BBDA5C400961DEA /* WalletManagerBridge.m */,
				13B07FB01A68108700A75B9A /* AppDelegate.mm */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				FD1CBD112C06098500CBB41F /* assetifyWithText.svg */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */,
				13B07FB71A68108700A75B9A /* main.m */,
				FD4DD9C22BBD868C00B1C105 /* WalletManager.swift */,
				FD679AE92BBDA3B700961DEA /* WalletManagerBridge.swift */,
				FD4DD9C72BBD8ED300B1C105 /* AssetifyApp-Bridging-Header.h */,
			);
			name = AssetifyApp;
			sourceTree = "<group>";
		};
		14A47ED22D413BF500A506E9 /* Recovered References */ = {
			isa = PBXGroup;
			children = (
			);
			name = "Recovered References";
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				DB3C728ADAB11E94C309257D /* libPods-AssetifyApp.a */,
				4C254F4F0E13978373DE075C /* libPods-AssetifyLoans.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		45F55228FDB7404AAE691BB8 /* Resources */ = {
			isa = PBXGroup;
			children = (
				D2305C5EA91944C4BBFE6CEF /* Poppins-Light.ttf */,
				8928AE5ED0174BC08F0B21CE /* SF-Pro.ttf */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
				8B1980E82AE7B44700CA05B3 /* RNColorThief.xcodeproj */,
				8B02E9A82AE673A200CEF93F /* RNInAppBrowser.xcodeproj */,
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				1402DCB62D2BF5C80041F37C /* libmpc_core_bindings.h */,
				1402DCB52D2BF5A70041F37C /* NativeSampleModule.h */,
				1402DCB22D2BF57C0041F37C /* NativeSampleModule.cpp */,
				FD5F5E5A2BB3159000CAC9C6 /* PrivacyInfo.xcprivacy */,
				8B4A6D182B060D5300599D81 /* GoogleService-Info.plist */,
				13B07FAE1A68108700A75B9A /* AssetifyApp */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* AssetifyAppTests */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
				45F55228FDB7404AAE691BB8 /* Resources */,
				1402DCB72D2BFCBB0041F37C /* MPCBindings.xcframework */,
				140C6C3F2D3FC8D6001E0162 /* AssetifyLoans.plist */,
				14A47ED22D413BF500A506E9 /* Recovered References */,
				14A47EFD2D413D9F00A506E9 /* AssetifyApp copy-Info.plist */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* AssetifyApp.app */,
				00E356EE1AD99517003FC87E /* AssetifyAppTests.xctest */,
				14A47EFC2D413D9F00A506E9 /* AssetifyLoans.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		8B02E9A92AE673A200CEF93F /* Products */ = {
			isa = PBXGroup;
			children = (
				8B02E9AD2AE673A200CEF93F /* libRNInAppBrowser.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		8B1980E92AE7B44700CA05B3 /* Products */ = {
			isa = PBXGroup;
			children = (
				8B1980EE2AE7B44A00CA05B3 /* libRNColorThief.a */,
				8B1980F02AE7B44A00CA05B3 /* libRNColorThief-tvOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				F296B38F2EEE2EE7447E78D5 /* Pods-AssetifyApp.debug.xcconfig */,
				CCD1FB6351F5AE779B2A07AF /* Pods-AssetifyApp.release.xcconfig */,
				14AFBC381DE0A4AA75C2B415 /* Pods-AssetifyLoans.debug.xcconfig */,
				C61BADD5FB8908CE852A7AD2 /* Pods-AssetifyLoans.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		00E356ED1AD99517003FC87E /* AssetifyAppTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "AssetifyAppTests" */;
			buildPhases = (
				00E356EA1AD99517003FC87E /* Sources */,
				00E356EB1AD99517003FC87E /* Frameworks */,
				00E356EC1AD99517003FC87E /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				00E356F51AD99517003FC87E /* PBXTargetDependency */,
			);
			name = AssetifyAppTests;
			productName = AssetifyAppTests;
			productReference = 00E356EE1AD99517003FC87E /* AssetifyAppTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		13B07F861A680F5B00A75B9A /* AssetifyApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "AssetifyApp" */;
			buildPhases = (
				CB64157FC4F04EF4C5A3EE59 /* [CP] Check Pods Manifest.lock */,
				FD10A7F022414F080027D42C /* Start Packager */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				8B83E08E2B88DD4F00D5D9E5 /* Embed Foundation Extensions */,
				A752AC8E9360441A9BB65B78 /* Upload Debug Symbols to Sentry */,
				4A8B1E71278300948C8518FF /* [CP] Embed Pods Frameworks */,
				CA03442821AC0054BAF8BDCA /* [CP] Copy Pods Resources */,
				219AF04664A05E39BAF9D80D /* [CP-User] [RNFB] Core Configuration */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = AssetifyApp;
			packageProductDependencies = (
				FD36FFC82BBED859004C827C /* SwiftProtobuf */,
				FD36FFCA2BBED859004C827C /* WalletCore */,
			);
			productName = AssetifyApp;
			productReference = 13B07F961A680F5B00A75B9A /* AssetifyApp.app */;
			productType = "com.apple.product-type.application";
		};
		14A47EDA2D413D9F00A506E9 /* AssetifyLoans */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 14A47EF92D413D9F00A506E9 /* Build configuration list for PBXNativeTarget "AssetifyLoans" */;
			buildPhases = (
				8DD98DE7B7F28C04DDC80137 /* [CP] Check Pods Manifest.lock */,
				14A47EDE2D413D9F00A506E9 /* Start Packager */,
				14A47EDF2D413D9F00A506E9 /* Sources */,
				14A47EE62D413D9F00A506E9 /* Frameworks */,
				14A47EEB2D413D9F00A506E9 /* Resources */,
				14A47EF32D413D9F00A506E9 /* Bundle React Native code and images */,
				14A47EF42D413D9F00A506E9 /* Embed Foundation Extensions */,
				14A47EF52D413D9F00A506E9 /* Upload Debug Symbols to Sentry */,
				E6B821288D8EECC80A420A94 /* [CP] Embed Pods Frameworks */,
				8A4B26259300158F044626E3 /* [CP] Copy Pods Resources */,
				25CACFFE97B9C9006C24E411 /* [CP-User] [RNFB] Core Configuration */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = AssetifyLoans;
			packageProductDependencies = (
				14A47EDB2D413D9F00A506E9 /* SwiftProtobuf */,
				14A47EDD2D413D9F00A506E9 /* WalletCore */,
			);
			productName = AssetifyApp;
			productReference = 14A47EFC2D413D9F00A506E9 /* AssetifyLoans.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = NO;
				LastSwiftUpdateCheck = 1520;
				LastUpgradeCheck = 1210;
				TargetAttributes = {
					00E356ED1AD99517003FC87E = {
						CreatedOnToolsVersion = 6.2;
						TestTargetID = 13B07F861A680F5B00A75B9A;
					};
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1520;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "AssetifyApp" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			packageReferences = (
				FD36FFC72BBED859004C827C /* XCRemoteSwiftPackageReference "wallet-core" */,
			);
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectReferences = (
				{
					ProductGroup = 8B1980E92AE7B44700CA05B3 /* Products */;
					ProjectRef = 8B1980E82AE7B44700CA05B3 /* RNColorThief.xcodeproj */;
				},
				{
					ProductGroup = 8B02E9A92AE673A200CEF93F /* Products */;
					ProjectRef = 8B02E9A82AE673A200CEF93F /* RNInAppBrowser.xcodeproj */;
				},
			);
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* AssetifyApp */,
				00E356ED1AD99517003FC87E /* AssetifyAppTests */,
				14A47EDA2D413D9F00A506E9 /* AssetifyLoans */,
			);
		};
/* End PBXProject section */

/* Begin PBXReferenceProxy section */
		8B02E9AD2AE673A200CEF93F /* libRNInAppBrowser.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRNInAppBrowser.a;
			remoteRef = 8B02E9AC2AE673A200CEF93F /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		8B1980EE2AE7B44A00CA05B3 /* libRNColorThief.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRNColorThief.a;
			remoteRef = 8B1980ED2AE7B44A00CA05B3 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		8B1980F02AE7B44A00CA05B3 /* libRNColorThief-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRNColorThief-tvOS.a";
			remoteRef = 8B1980EF2AE7B44A00CA05B3 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
/* End PBXReferenceProxy section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				FD1CBD122C06098500CBB41F /* assetifyWithText.svg in Resources */,
				81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				FD5F5E5B2BB3159000CAC9C6 /* PrivacyInfo.xcprivacy in Resources */,
				8B4A6D192B060D5300599D81 /* GoogleService-Info.plist in Resources */,
				99F0D6ED2B1D4FB3BFFC80EF /* Poppins-Light.ttf in Resources */,
				916107B39E5B49168F12660C /* SF-Pro.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		14A47EEB2D413D9F00A506E9 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				14A47EEC2D413D9F00A506E9 /* assetifyWithText.svg in Resources */,
				14A47EED2D413D9F00A506E9 /* LaunchScreen.storyboard in Resources */,
				14A47EEE2D413D9F00A506E9 /* Images.xcassets in Resources */,
				14A47EEF2D413D9F00A506E9 /* PrivacyInfo.xcprivacy in Resources */,
				14A47EF02D413D9F00A506E9 /* GoogleService-Info.plist in Resources */,
				14A47EF12D413D9F00A506E9 /* Poppins-Light.ttf in Resources */,
				14A47EF22D413D9F00A506E9 /* SF-Pro.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"../node_modules/react-native/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"../node_modules/react-native/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT \\\"/bin/sh ../node_modules/@sentry/react-native/scripts/sentry-xcode.sh $REACT_NATIVE_XCODE\\\"\"\n";
		};
		14A47EDE2D413D9F00A506E9 /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
		14A47EF32D413D9F00A506E9 /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"../node_modules/react-native/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"../node_modules/react-native/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT \\\"/bin/sh ../node_modules/@sentry/react-native/scripts/sentry-xcode.sh $REACT_NATIVE_XCODE\\\"\"\n";
		};
		14A47EF52D413D9F00A506E9 /* Upload Debug Symbols to Sentry */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "Upload Debug Symbols to Sentry";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh ../node_modules/@sentry/react-native/scripts/sentry-xcode-debug-files.sh\n";
		};
		219AF04664A05E39BAF9D80D /* [CP-User] [RNFB] Core Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Core Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\n\n##########################################################################\n##########################################################################\n#\n#  NOTE THAT IF YOU CHANGE THIS FILE YOU MUST RUN pod install AFTERWARDS\n#\n#  This file is installed as an Xcode build script in the project file\n#  by cocoapods, and you will not see your changes until you pod install\n#\n##########################################################################\n##########################################################################\n\nset -e\n\n_MAX_LOOKUPS=2;\n_SEARCH_RESULT=''\n_RN_ROOT_EXISTS=''\n_CURRENT_LOOKUPS=1\n_JSON_ROOT=\"'react-native'\"\n_JSON_FILE_NAME='firebase.json'\n_JSON_OUTPUT_BASE64='e30=' # { }\n_CURRENT_SEARCH_DIR=${PROJECT_DIR}\n_PLIST_BUDDY=/usr/libexec/PlistBuddy\n_TARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${INFOPLIST_PATH}\"\n_DSYM_PLIST=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist\"\n\n# plist arrays\n_PLIST_ENTRY_KEYS=()\n_PLIST_ENTRY_TYPES=()\n_PLIST_ENTRY_VALUES=()\n\nfunction setPlistValue {\n  echo \"info:      setting plist entry '$1' of type '$2' in file '$4'\"\n  ${_PLIST_BUDDY} -c \"Add :$1 $2 '$3'\" $4 || echo \"info:      '$1' already exists\"\n}\n\nfunction getFirebaseJsonKeyValue () {\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$1'); puts output[$_JSON_ROOT]['$2']\"\n  else\n    echo \"\"\n  fi;\n}\n\nfunction jsonBoolToYesNo () {\n  if [[ $1 == \"false\" ]]; then\n    echo \"NO\"\n  elif [[ $1 == \"true\" ]]; then\n    echo \"YES\"\n  else echo \"NO\"\n  fi\n}\n\necho \"info: -> RNFB build script started\"\necho \"info: 1) Locating ${_JSON_FILE_NAME} file:\"\n\nif [[ -z ${_CURRENT_SEARCH_DIR} ]]; then\n  _CURRENT_SEARCH_DIR=$(pwd)\nfi;\n\nwhile true; do\n  _CURRENT_SEARCH_DIR=$(dirname \"$_CURRENT_SEARCH_DIR\")\n  if [[ \"$_CURRENT_SEARCH_DIR\" == \"/\" ]] || [[ ${_CURRENT_LOOKUPS} -gt ${_MAX_LOOKUPS} ]]; then break; fi;\n  echo \"info:      ($_CURRENT_LOOKUPS of $_MAX_LOOKUPS) Searching in '$_CURRENT_SEARCH_DIR' for a ${_JSON_FILE_NAME} file.\"\n  _SEARCH_RESULT=$(find \"$_CURRENT_SEARCH_DIR\" -maxdepth 2 -name ${_JSON_FILE_NAME} -print | /usr/bin/head -n 1)\n  if [[ ${_SEARCH_RESULT} ]]; then\n    echo \"info:      ${_JSON_FILE_NAME} found at $_SEARCH_RESULT\"\n    break;\n  fi;\n  _CURRENT_LOOKUPS=$((_CURRENT_LOOKUPS+1))\ndone\n\nif [[ ${_SEARCH_RESULT} ]]; then\n  _JSON_OUTPUT_RAW=$(cat \"${_SEARCH_RESULT}\")\n  _RN_ROOT_EXISTS=$(ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$_JSON_OUTPUT_RAW'); puts output[$_JSON_ROOT]\" || echo '')\n\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    if ! python3 --version >/dev/null 2>&1; then echo \"python3 not found, firebase.json file processing error.\" && exit 1; fi\n    _JSON_OUTPUT_BASE64=$(python3 -c 'import json,sys,base64;print(base64.b64encode(bytes(json.dumps(json.loads(open('\"'${_SEARCH_RESULT}'\"', '\"'rb'\"').read())['${_JSON_ROOT}']), '\"'utf-8'\"')).decode())' || echo \"e30=\")\n  fi\n\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n\n  # config.app_data_collection_default_enabled\n  _APP_DATA_COLLECTION_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_data_collection_default_enabled\")\n  if [[ $_APP_DATA_COLLECTION_ENABLED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseDataCollectionDefaultEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_DATA_COLLECTION_ENABLED\")\")\n  fi\n\n  # config.analytics_auto_collection_enabled\n  _ANALYTICS_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_auto_collection_enabled\")\n  if [[ $_ANALYTICS_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_COLLECTION\")\")\n  fi\n\n  # config.analytics_collection_deactivated\n  _ANALYTICS_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_collection_deactivated\")\n  if [[ $_ANALYTICS_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_DEACTIVATED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_DEACTIVATED\")\")\n  fi\n\n  # config.analytics_idfv_collection_enabled\n  _ANALYTICS_IDFV_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_idfv_collection_enabled\")\n  if [[ $_ANALYTICS_IDFV_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_IDFV_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_IDFV_COLLECTION\")\")\n  fi\n\n  # config.analytics_default_allow_analytics_storage\n  _ANALYTICS_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_analytics_storage\")\n  if [[ $_ANALYTICS_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_ANALYTICS_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_storage\n  _ANALYTICS_AD_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_storage\")\n  if [[ $_ANALYTICS_AD_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_user_data\n  _ANALYTICS_AD_USER_DATA=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_user_data\")\n  if [[ $_ANALYTICS_AD_USER_DATA ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_USER_DATA\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_USER_DATA\")\")\n  fi\n\n  # config.analytics_default_allow_ad_personalization_signals\n  _ANALYTICS_PERSONALIZATION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_personalization_signals\")\n  if [[ $_ANALYTICS_PERSONALIZATION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_PERSONALIZATION\")\")\n  fi\n\n  # config.analytics_registration_with_ad_network_enabled\n  _ANALYTICS_REGISTRATION_WITH_AD_NETWORK=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_registration_with_ad_network_enabled\")\n  if [[ $_ANALYTICS_REGISTRATION_WITH_AD_NETWORK ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_REGISTRATION_WITH_AD_NETWORK_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_REGISTRATION_WITH_AD_NETWORK\")\")\n  fi\n\n  # config.google_analytics_automatic_screen_reporting_enabled\n  _ANALYTICS_AUTO_SCREEN_REPORTING=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_automatic_screen_reporting_enabled\")\n  if [[ $_ANALYTICS_AUTO_SCREEN_REPORTING ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAutomaticScreenReportingEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_SCREEN_REPORTING\")\")\n  fi\n\n  # config.perf_auto_collection_enabled\n  _PERF_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_auto_collection_enabled\")\n  if [[ $_PERF_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_enabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_AUTO_COLLECTION\")\")\n  fi\n\n  # config.perf_collection_deactivated\n  _PERF_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_collection_deactivated\")\n  if [[ $_PERF_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_deactivated\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_DEACTIVATED\")\")\n  fi\n\n  # config.messaging_auto_init_enabled\n  _MESSAGING_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"messaging_auto_init_enabled\")\n  if [[ $_MESSAGING_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseMessagingAutoInitEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_MESSAGING_AUTO_INIT\")\")\n  fi\n\n  # config.in_app_messaging_auto_colllection_enabled\n  _FIAM_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"in_app_messaging_auto_collection_enabled\")\n  if [[ $_FIAM_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseInAppMessagingAutomaticDataCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_FIAM_AUTO_INIT\")\")\n  fi\n\n  # config.app_check_token_auto_refresh\n  _APP_CHECK_TOKEN_AUTO_REFRESH=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_check_token_auto_refresh\")\n  if [[ $_APP_CHECK_TOKEN_AUTO_REFRESH ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAppCheckTokenAutoRefreshEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_CHECK_TOKEN_AUTO_REFRESH\")\")\n  fi\n\n  # config.crashlytics_disable_auto_disabler - undocumented for now - mainly for debugging, document if becomes useful\n  _CRASHLYTICS_AUTO_DISABLE_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"crashlytics_disable_auto_disabler\")\n  if [[ $_CRASHLYTICS_AUTO_DISABLE_ENABLED == \"true\" ]]; then\n    echo \"Disabled Crashlytics auto disabler.\" # do nothing\n  else\n    _PLIST_ENTRY_KEYS+=(\"FirebaseCrashlyticsCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"NO\")\n  fi\nelse\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n  echo \"warning:   A firebase.json file was not found, whilst this file is optional it is recommended to include it to configure firebase services in React Native Firebase.\"\nfi;\n\necho \"info: 2) Injecting Info.plist entries: \"\n\n# Log out the keys we're adding\nfor i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n  echo \"    ->  $i) ${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\"\ndone\n\nfor plist in \"${_TARGET_PLIST}\" \"${_DSYM_PLIST}\" ; do\n  if [[ -f \"${plist}\" ]]; then\n\n    # paths with spaces break the call to setPlistValue. temporarily modify\n    # the shell internal field separator variable (IFS), which normally\n    # includes spaces, to consist only of line breaks\n    oldifs=$IFS\n    IFS=\"\n\"\n\n    for i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n      setPlistValue \"${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\" \"${plist}\"\n    done\n\n    # restore the original internal field separator value\n    IFS=$oldifs\n  else\n    echo \"warning:   A Info.plist build output file was not found (${plist})\"\n  fi\ndone\n\necho \"info: <- RNFB build script finished\"\n";
		};
		25CACFFE97B9C9006C24E411 /* [CP-User] [RNFB] Core Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Core Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\n\n##########################################################################\n##########################################################################\n#\n#  NOTE THAT IF YOU CHANGE THIS FILE YOU MUST RUN pod install AFTERWARDS\n#\n#  This file is installed as an Xcode build script in the project file\n#  by cocoapods, and you will not see your changes until you pod install\n#\n##########################################################################\n##########################################################################\n\nset -e\n\n_MAX_LOOKUPS=2;\n_SEARCH_RESULT=''\n_RN_ROOT_EXISTS=''\n_CURRENT_LOOKUPS=1\n_JSON_ROOT=\"'react-native'\"\n_JSON_FILE_NAME='firebase.json'\n_JSON_OUTPUT_BASE64='e30=' # { }\n_CURRENT_SEARCH_DIR=${PROJECT_DIR}\n_PLIST_BUDDY=/usr/libexec/PlistBuddy\n_TARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${INFOPLIST_PATH}\"\n_DSYM_PLIST=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist\"\n\n# plist arrays\n_PLIST_ENTRY_KEYS=()\n_PLIST_ENTRY_TYPES=()\n_PLIST_ENTRY_VALUES=()\n\nfunction setPlistValue {\n  echo \"info:      setting plist entry '$1' of type '$2' in file '$4'\"\n  ${_PLIST_BUDDY} -c \"Add :$1 $2 '$3'\" $4 || echo \"info:      '$1' already exists\"\n}\n\nfunction getFirebaseJsonKeyValue () {\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$1'); puts output[$_JSON_ROOT]['$2']\"\n  else\n    echo \"\"\n  fi;\n}\n\nfunction jsonBoolToYesNo () {\n  if [[ $1 == \"false\" ]]; then\n    echo \"NO\"\n  elif [[ $1 == \"true\" ]]; then\n    echo \"YES\"\n  else echo \"NO\"\n  fi\n}\n\necho \"info: -> RNFB build script started\"\necho \"info: 1) Locating ${_JSON_FILE_NAME} file:\"\n\nif [[ -z ${_CURRENT_SEARCH_DIR} ]]; then\n  _CURRENT_SEARCH_DIR=$(pwd)\nfi;\n\nwhile true; do\n  _CURRENT_SEARCH_DIR=$(dirname \"$_CURRENT_SEARCH_DIR\")\n  if [[ \"$_CURRENT_SEARCH_DIR\" == \"/\" ]] || [[ ${_CURRENT_LOOKUPS} -gt ${_MAX_LOOKUPS} ]]; then break; fi;\n  echo \"info:      ($_CURRENT_LOOKUPS of $_MAX_LOOKUPS) Searching in '$_CURRENT_SEARCH_DIR' for a ${_JSON_FILE_NAME} file.\"\n  _SEARCH_RESULT=$(find \"$_CURRENT_SEARCH_DIR\" -maxdepth 2 -name ${_JSON_FILE_NAME} -print | /usr/bin/head -n 1)\n  if [[ ${_SEARCH_RESULT} ]]; then\n    echo \"info:      ${_JSON_FILE_NAME} found at $_SEARCH_RESULT\"\n    break;\n  fi;\n  _CURRENT_LOOKUPS=$((_CURRENT_LOOKUPS+1))\ndone\n\nif [[ ${_SEARCH_RESULT} ]]; then\n  _JSON_OUTPUT_RAW=$(cat \"${_SEARCH_RESULT}\")\n  _RN_ROOT_EXISTS=$(ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$_JSON_OUTPUT_RAW'); puts output[$_JSON_ROOT]\" || echo '')\n\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    if ! python3 --version >/dev/null 2>&1; then echo \"python3 not found, firebase.json file processing error.\" && exit 1; fi\n    _JSON_OUTPUT_BASE64=$(python3 -c 'import json,sys,base64;print(base64.b64encode(bytes(json.dumps(json.loads(open('\"'${_SEARCH_RESULT}'\"', '\"'rb'\"').read())['${_JSON_ROOT}']), '\"'utf-8'\"')).decode())' || echo \"e30=\")\n  fi\n\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n\n  # config.app_data_collection_default_enabled\n  _APP_DATA_COLLECTION_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_data_collection_default_enabled\")\n  if [[ $_APP_DATA_COLLECTION_ENABLED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseDataCollectionDefaultEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_DATA_COLLECTION_ENABLED\")\")\n  fi\n\n  # config.analytics_auto_collection_enabled\n  _ANALYTICS_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_auto_collection_enabled\")\n  if [[ $_ANALYTICS_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_COLLECTION\")\")\n  fi\n\n  # config.analytics_collection_deactivated\n  _ANALYTICS_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_collection_deactivated\")\n  if [[ $_ANALYTICS_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_DEACTIVATED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_DEACTIVATED\")\")\n  fi\n\n  # config.analytics_idfv_collection_enabled\n  _ANALYTICS_IDFV_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_idfv_collection_enabled\")\n  if [[ $_ANALYTICS_IDFV_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_IDFV_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_IDFV_COLLECTION\")\")\n  fi\n\n  # config.analytics_default_allow_analytics_storage\n  _ANALYTICS_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_analytics_storage\")\n  if [[ $_ANALYTICS_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_ANALYTICS_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_storage\n  _ANALYTICS_AD_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_storage\")\n  if [[ $_ANALYTICS_AD_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_user_data\n  _ANALYTICS_AD_USER_DATA=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_user_data\")\n  if [[ $_ANALYTICS_AD_USER_DATA ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_USER_DATA\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_USER_DATA\")\")\n  fi\n\n  # config.analytics_default_allow_ad_personalization_signals\n  _ANALYTICS_PERSONALIZATION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_personalization_signals\")\n  if [[ $_ANALYTICS_PERSONALIZATION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_PERSONALIZATION\")\")\n  fi\n\n  # config.analytics_registration_with_ad_network_enabled\n  _ANALYTICS_REGISTRATION_WITH_AD_NETWORK=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_registration_with_ad_network_enabled\")\n  if [[ $_ANALYTICS_REGISTRATION_WITH_AD_NETWORK ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_REGISTRATION_WITH_AD_NETWORK_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_REGISTRATION_WITH_AD_NETWORK\")\")\n  fi\n\n  # config.google_analytics_automatic_screen_reporting_enabled\n  _ANALYTICS_AUTO_SCREEN_REPORTING=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_automatic_screen_reporting_enabled\")\n  if [[ $_ANALYTICS_AUTO_SCREEN_REPORTING ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAutomaticScreenReportingEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_SCREEN_REPORTING\")\")\n  fi\n\n  # config.perf_auto_collection_enabled\n  _PERF_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_auto_collection_enabled\")\n  if [[ $_PERF_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_enabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_AUTO_COLLECTION\")\")\n  fi\n\n  # config.perf_collection_deactivated\n  _PERF_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_collection_deactivated\")\n  if [[ $_PERF_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_deactivated\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_DEACTIVATED\")\")\n  fi\n\n  # config.messaging_auto_init_enabled\n  _MESSAGING_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"messaging_auto_init_enabled\")\n  if [[ $_MESSAGING_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseMessagingAutoInitEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_MESSAGING_AUTO_INIT\")\")\n  fi\n\n  # config.in_app_messaging_auto_colllection_enabled\n  _FIAM_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"in_app_messaging_auto_collection_enabled\")\n  if [[ $_FIAM_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseInAppMessagingAutomaticDataCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_FIAM_AUTO_INIT\")\")\n  fi\n\n  # config.app_check_token_auto_refresh\n  _APP_CHECK_TOKEN_AUTO_REFRESH=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_check_token_auto_refresh\")\n  if [[ $_APP_CHECK_TOKEN_AUTO_REFRESH ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAppCheckTokenAutoRefreshEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_CHECK_TOKEN_AUTO_REFRESH\")\")\n  fi\n\n  # config.crashlytics_disable_auto_disabler - undocumented for now - mainly for debugging, document if becomes useful\n  _CRASHLYTICS_AUTO_DISABLE_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"crashlytics_disable_auto_disabler\")\n  if [[ $_CRASHLYTICS_AUTO_DISABLE_ENABLED == \"true\" ]]; then\n    echo \"Disabled Crashlytics auto disabler.\" # do nothing\n  else\n    _PLIST_ENTRY_KEYS+=(\"FirebaseCrashlyticsCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"NO\")\n  fi\nelse\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n  echo \"warning:   A firebase.json file was not found, whilst this file is optional it is recommended to include it to configure firebase services in React Native Firebase.\"\nfi;\n\necho \"info: 2) Injecting Info.plist entries: \"\n\n# Log out the keys we're adding\nfor i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n  echo \"    ->  $i) ${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\"\ndone\n\nfor plist in \"${_TARGET_PLIST}\" \"${_DSYM_PLIST}\" ; do\n  if [[ -f \"${plist}\" ]]; then\n\n    # paths with spaces break the call to setPlistValue. temporarily modify\n    # the shell internal field separator variable (IFS), which normally\n    # includes spaces, to consist only of line breaks\n    oldifs=$IFS\n    IFS=\"\n\"\n\n    for i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n      setPlistValue \"${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\" \"${plist}\"\n    done\n\n    # restore the original internal field separator value\n    IFS=$oldifs\n  else\n    echo \"warning:   A Info.plist build output file was not found (${plist})\"\n  fi\ndone\n\necho \"info: <- RNFB build script finished\"\n";
		};
		4A8B1E71278300948C8518FF /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-AssetifyApp/Pods-AssetifyApp-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-AssetifyApp/Pods-AssetifyApp-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-AssetifyApp/Pods-AssetifyApp-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		8A4B26259300158F044626E3 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-AssetifyLoans/Pods-AssetifyLoans-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-AssetifyLoans/Pods-AssetifyLoans-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-AssetifyLoans/Pods-AssetifyLoans-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		8DD98DE7B7F28C04DDC80137 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-AssetifyLoans-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		A752AC8E9360441A9BB65B78 /* Upload Debug Symbols to Sentry */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "Upload Debug Symbols to Sentry";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh ../node_modules/@sentry/react-native/scripts/sentry-xcode-debug-files.sh\n";
		};
		CA03442821AC0054BAF8BDCA /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-AssetifyApp/Pods-AssetifyApp-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-AssetifyApp/Pods-AssetifyApp-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-AssetifyApp/Pods-AssetifyApp-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		CB64157FC4F04EF4C5A3EE59 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-AssetifyApp-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		E6B821288D8EECC80A420A94 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-AssetifyLoans/Pods-AssetifyLoans-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-AssetifyLoans/Pods-AssetifyLoans-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-AssetifyLoans/Pods-AssetifyLoans-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		FD10A7F022414F080027D42C /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		00E356EA1AD99517003FC87E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				00E356F31AD99517003FC87E /* AssetifyAppTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				FD679AEC2BBDA5C400961DEA /* WalletManagerBridge.m in Sources */,
				FD4DD9C32BBD868C00B1C105 /* WalletManager.swift in Sources */,
				FD679AEA2BBDA3B700961DEA /* WalletManagerBridge.swift in Sources */,
				E4A77B4B2DCCC504009CAAB4 /* TransactionManager.swift in Sources */,
				13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */,
				1402DCB32D2BF57C0041F37C /* NativeSampleModule.cpp in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		14A47EDF2D413D9F00A506E9 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				14A47EE02D413D9F00A506E9 /* WalletManagerBridge.m in Sources */,
				14A47EE12D413D9F00A506E9 /* WalletManager.swift in Sources */,
				14A47EE22D413D9F00A506E9 /* WalletManagerBridge.swift in Sources */,
				E4A77B4A2DCCC504009CAAB4 /* TransactionManager.swift in Sources */,
				14A47EE32D413D9F00A506E9 /* AppDelegate.mm in Sources */,
				14A47EE42D413D9F00A506E9 /* NativeSampleModule.cpp in Sources */,
				14A47EE52D413D9F00A506E9 /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* AssetifyApp */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		00E356F61AD99517003FC87E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				DEVELOPMENT_TEAM = 3267WLJ7GG;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = AssetifyAppTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/AssetifyApp.app/AssetifyApp";
			};
			name = Debug;
		};
		00E356F71AD99517003FC87E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				COPY_PHASE_STRIP = NO;
				DEVELOPMENT_TEAM = 3267WLJ7GG;
				INFOPLIST_FILE = AssetifyAppTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/AssetifyApp.app/AssetifyApp";
			};
			name = Release;
		};
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F296B38F2EEE2EE7447E78D5 /* Pods-AssetifyApp.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = AssetifyApp/AssetifyApp.entitlements;
				CURRENT_PROJECT_VERSION = 130100011;
				DEVELOPMENT_TEAM = 3267WLJ7GG;
				ENABLE_BITCODE = NO;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/BVLinearGradient\"",
					"\"${PODS_ROOT}/Headers/Public/DoubleConversion\"",
					"\"${PODS_ROOT}/Headers/Public/FBLazyVector\"",
					"\"${PODS_ROOT}/Headers/Public/Firebase\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseCore\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseCoreExtension\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseInstallations\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseMessaging\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleDataTransport\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleUtilities\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-Camera\"",
					"\"${PODS_ROOT}/Headers/Public/PromisesObjC\"",
					"\"${PODS_ROOT}/Headers/Public/RCT-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/RCTDeprecation\"",
					"\"${PODS_ROOT}/Headers/Public/RCTRequired\"",
					"\"${PODS_ROOT}/Headers/Public/RCTTypeSafety\"",
					"\"${PODS_ROOT}/Headers/Public/RNCAsyncStorage\"",
					"\"${PODS_ROOT}/Headers/Public/RNCClipboard\"",
					"\"${PODS_ROOT}/Headers/Public/RNDeviceInfo\"",
					"\"${PODS_ROOT}/Headers/Public/RNExitApp\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBAnalytics\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBApp\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBMessaging\"",
					"\"${PODS_ROOT}/Headers/Public/RNFS\"",
					"\"${PODS_ROOT}/Headers/Public/RNGestureHandler\"",
					"\"${PODS_ROOT}/Headers/Public/RNInAppBrowser\"",
					"\"${PODS_ROOT}/Headers/Public/RNOS\"",
					"\"${PODS_ROOT}/Headers/Public/RNPermissions\"",
					"\"${PODS_ROOT}/Headers/Public/RNReactNativeHapticFeedback\"",
					"\"${PODS_ROOT}/Headers/Public/RNReanimated\"",
					"\"${PODS_ROOT}/Headers/Public/RNSVG\"",
					"\"${PODS_ROOT}/Headers/Public/RNScreens\"",
					"\"${PODS_ROOT}/Headers/Public/RNSentry\"",
					"\"${PODS_ROOT}/Headers/Public/RNVectorIcons\"",
					"\"${PODS_ROOT}/Headers/Public/React-Core\"",
					"\"${PODS_ROOT}/Headers/Public/React-Fabric\"",
					"\"${PODS_ROOT}/Headers/Public/React-FabricComponents\"",
					"\"${PODS_ROOT}/Headers/Public/React-FabricImage\"",
					"\"${PODS_ROOT}/Headers/Public/React-ImageManager\"",
					"\"${PODS_ROOT}/Headers/Public/React-Mapbuffer\"",
					"\"${PODS_ROOT}/Headers/Public/React-NativeModulesApple\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTAnimation\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTAppDelegate\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTBlob\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTFabric\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTText\"",
					"\"${PODS_ROOT}/Headers/Public/React-RuntimeApple\"",
					"\"${PODS_ROOT}/Headers/Public/React-RuntimeCore\"",
					"\"${PODS_ROOT}/Headers/Public/React-callinvoker\"",
					"\"${PODS_ROOT}/Headers/Public/React-cxxreact\"",
					"\"${PODS_ROOT}/Headers/Public/React-debug\"",
					"\"${PODS_ROOT}/Headers/Public/React-defaultsnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-domnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-featureflags\"",
					"\"${PODS_ROOT}/Headers/Public/React-featureflagsnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-graphics\"",
					"\"${PODS_ROOT}/Headers/Public/React-idlecallbacksnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsc\"",
					"\"${PODS_ROOT}/Headers/Public/React-jserrorhandler\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsi\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsiexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsinspector\"",
					"\"${PODS_ROOT}/Headers/Public/React-logger\"",
					"\"${PODS_ROOT}/Headers/Public/React-microtasksnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-nativeconfig\"",
					"\"${PODS_ROOT}/Headers/Public/React-perflogger\"",
					"\"${PODS_ROOT}/Headers/Public/React-performancetimeline\"",
					"\"${PODS_ROOT}/Headers/Public/React-rendererconsistency\"",
					"\"${PODS_ROOT}/Headers/Public/React-rendererdebug\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimeexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimescheduler\"",
					"\"${PODS_ROOT}/Headers/Public/React-timing\"",
					"\"${PODS_ROOT}/Headers/Public/React-utils\"",
					"\"${PODS_ROOT}/Headers/Public/ReactCodegen\"",
					"\"${PODS_ROOT}/Headers/Public/ReactCommon\"",
					"\"${PODS_ROOT}/Headers/Public/ReactNativeCameraKit\"",
					"\"${PODS_ROOT}/Headers/Public/ReactNativeExceptionHandler\"",
					"\"${PODS_ROOT}/Headers/Public/Sentry\"",
					"\"${PODS_ROOT}/Headers/Public/SocketRocket\"",
					"\"${PODS_ROOT}/Headers/Public/Yoga\"",
					"\"${PODS_ROOT}/Headers/Public/boost\"",
					"\"${PODS_ROOT}/Headers/Public/breez_sdk\"",
					"\"${PODS_ROOT}/Headers/Public/fmt\"",
					"\"${PODS_ROOT}/Headers/Public/glog\"",
					"\"${PODS_ROOT}/Headers/Public/lottie-react-native\"",
					"\"${PODS_ROOT}/Headers/Public/nanopb\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-biometrics\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-blur\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-get-random-values\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-netinfo\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-onesignal\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-randombytes\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-safe-area-context\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-webview\"",
					"\"$(PODS_ROOT)/DoubleConversion\"",
					"$(inherited)",
					"${PODS_ROOT}/Firebase/CoreOnly/Sources",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/boost-for-react-native\"",
					"\"$(PODS_ROOT)/glog\"",
					"\"$(PODS_ROOT)/RCT-Folly\"",
					"\"$(PODS_ROOT)/Headers/Public/React-hermes\"",
					"\"$(PODS_ROOT)/Headers/Public/hermes-engine\"",
					"\"$(PODS_ROOT)/../../node_modules/react-native/ReactCommon\"",
					"\"$(PODS_ROOT)/Headers/Private/React-Core\"",
					"\"$(PODS_ROOT)/../libmpc_core_bindings.h\"",
				);
				INFOPLIST_FILE = AssetifyApp/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Asstify;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.11;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = net.assetify.assetifyApp;
				PRODUCT_NAME = AssetifyApp;
				SWIFT_OBJC_BRIDGING_HEADER = "AssetifyApp-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CCD1FB6351F5AE779B2A07AF /* Pods-AssetifyApp.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = AssetifyApp/AssetifyApp.entitlements;
				CURRENT_PROJECT_VERSION = 130100011;
				DEVELOPMENT_TEAM = 3267WLJ7GG;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/BVLinearGradient\"",
					"\"${PODS_ROOT}/Headers/Public/DoubleConversion\"",
					"\"${PODS_ROOT}/Headers/Public/FBLazyVector\"",
					"\"${PODS_ROOT}/Headers/Public/Firebase\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseCore\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseCoreExtension\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseInstallations\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseMessaging\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleDataTransport\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleUtilities\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-Camera\"",
					"\"${PODS_ROOT}/Headers/Public/PromisesObjC\"",
					"\"${PODS_ROOT}/Headers/Public/RCT-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/RCTDeprecation\"",
					"\"${PODS_ROOT}/Headers/Public/RCTRequired\"",
					"\"${PODS_ROOT}/Headers/Public/RCTTypeSafety\"",
					"\"${PODS_ROOT}/Headers/Public/RNCAsyncStorage\"",
					"\"${PODS_ROOT}/Headers/Public/RNCClipboard\"",
					"\"${PODS_ROOT}/Headers/Public/RNDeviceInfo\"",
					"\"${PODS_ROOT}/Headers/Public/RNExitApp\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBAnalytics\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBApp\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBMessaging\"",
					"\"${PODS_ROOT}/Headers/Public/RNFS\"",
					"\"${PODS_ROOT}/Headers/Public/RNGestureHandler\"",
					"\"${PODS_ROOT}/Headers/Public/RNInAppBrowser\"",
					"\"${PODS_ROOT}/Headers/Public/RNOS\"",
					"\"${PODS_ROOT}/Headers/Public/RNPermissions\"",
					"\"${PODS_ROOT}/Headers/Public/RNReactNativeHapticFeedback\"",
					"\"${PODS_ROOT}/Headers/Public/RNReanimated\"",
					"\"${PODS_ROOT}/Headers/Public/RNSVG\"",
					"\"${PODS_ROOT}/Headers/Public/RNScreens\"",
					"\"${PODS_ROOT}/Headers/Public/RNSentry\"",
					"\"${PODS_ROOT}/Headers/Public/RNVectorIcons\"",
					"\"${PODS_ROOT}/Headers/Public/React-Core\"",
					"\"${PODS_ROOT}/Headers/Public/React-Fabric\"",
					"\"${PODS_ROOT}/Headers/Public/React-FabricComponents\"",
					"\"${PODS_ROOT}/Headers/Public/React-FabricImage\"",
					"\"${PODS_ROOT}/Headers/Public/React-ImageManager\"",
					"\"${PODS_ROOT}/Headers/Public/React-Mapbuffer\"",
					"\"${PODS_ROOT}/Headers/Public/React-NativeModulesApple\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTAnimation\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTAppDelegate\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTBlob\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTFabric\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTText\"",
					"\"${PODS_ROOT}/Headers/Public/React-RuntimeApple\"",
					"\"${PODS_ROOT}/Headers/Public/React-RuntimeCore\"",
					"\"${PODS_ROOT}/Headers/Public/React-callinvoker\"",
					"\"${PODS_ROOT}/Headers/Public/React-cxxreact\"",
					"\"${PODS_ROOT}/Headers/Public/React-debug\"",
					"\"${PODS_ROOT}/Headers/Public/React-defaultsnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-domnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-featureflags\"",
					"\"${PODS_ROOT}/Headers/Public/React-featureflagsnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-graphics\"",
					"\"${PODS_ROOT}/Headers/Public/React-idlecallbacksnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsc\"",
					"\"${PODS_ROOT}/Headers/Public/React-jserrorhandler\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsi\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsiexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsinspector\"",
					"\"${PODS_ROOT}/Headers/Public/React-logger\"",
					"\"${PODS_ROOT}/Headers/Public/React-microtasksnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-nativeconfig\"",
					"\"${PODS_ROOT}/Headers/Public/React-perflogger\"",
					"\"${PODS_ROOT}/Headers/Public/React-performancetimeline\"",
					"\"${PODS_ROOT}/Headers/Public/React-rendererconsistency\"",
					"\"${PODS_ROOT}/Headers/Public/React-rendererdebug\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimeexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimescheduler\"",
					"\"${PODS_ROOT}/Headers/Public/React-timing\"",
					"\"${PODS_ROOT}/Headers/Public/React-utils\"",
					"\"${PODS_ROOT}/Headers/Public/ReactCodegen\"",
					"\"${PODS_ROOT}/Headers/Public/ReactCommon\"",
					"\"${PODS_ROOT}/Headers/Public/ReactNativeCameraKit\"",
					"\"${PODS_ROOT}/Headers/Public/ReactNativeExceptionHandler\"",
					"\"${PODS_ROOT}/Headers/Public/Sentry\"",
					"\"${PODS_ROOT}/Headers/Public/SocketRocket\"",
					"\"${PODS_ROOT}/Headers/Public/Yoga\"",
					"\"${PODS_ROOT}/Headers/Public/boost\"",
					"\"${PODS_ROOT}/Headers/Public/breez_sdk\"",
					"\"${PODS_ROOT}/Headers/Public/fmt\"",
					"\"${PODS_ROOT}/Headers/Public/glog\"",
					"\"${PODS_ROOT}/Headers/Public/lottie-react-native\"",
					"\"${PODS_ROOT}/Headers/Public/nanopb\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-biometrics\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-blur\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-get-random-values\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-netinfo\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-onesignal\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-randombytes\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-safe-area-context\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-webview\"",
					"\"$(PODS_ROOT)/DoubleConversion\"",
					"$(inherited)",
					"${PODS_ROOT}/Firebase/CoreOnly/Sources",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/boost-for-react-native\"",
					"\"$(PODS_ROOT)/glog\"",
					"\"$(PODS_ROOT)/RCT-Folly\"",
					"\"$(PODS_ROOT)/Headers/Public/React-hermes\"",
					"\"$(PODS_ROOT)/Headers/Public/hermes-engine\"",
					"\"$(PODS_ROOT)/../../node_modules/react-native/ReactCommon\"",
					"\"$(PODS_ROOT)/Headers/Private/React-Core\"",
					"\"$(PODS_ROOT)/../libmpc_core_bindings.h\"",
				);
				INFOPLIST_FILE = AssetifyApp/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Asstify;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.11;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = net.assetify.assetifyApp;
				PRODUCT_NAME = AssetifyApp;
				SWIFT_OBJC_BRIDGING_HEADER = "AssetifyApp-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		14A47EFA2D413D9F00A506E9 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 14AFBC381DE0A4AA75C2B415 /* Pods-AssetifyLoans.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = AssetifyApp/AssetifyApp.entitlements;
				CURRENT_PROJECT_VERSION = 130100011;
				DEVELOPMENT_TEAM = 3267WLJ7GG;
				ENABLE_BITCODE = NO;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/BVLinearGradient\"",
					"\"${PODS_ROOT}/Headers/Public/DoubleConversion\"",
					"\"${PODS_ROOT}/Headers/Public/FBLazyVector\"",
					"\"${PODS_ROOT}/Headers/Public/Firebase\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseCore\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseCoreExtension\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseInstallations\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseMessaging\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleDataTransport\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleUtilities\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-Camera\"",
					"\"${PODS_ROOT}/Headers/Public/PromisesObjC\"",
					"\"${PODS_ROOT}/Headers/Public/RCT-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/RCTDeprecation\"",
					"\"${PODS_ROOT}/Headers/Public/RCTRequired\"",
					"\"${PODS_ROOT}/Headers/Public/RCTTypeSafety\"",
					"\"${PODS_ROOT}/Headers/Public/RNCAsyncStorage\"",
					"\"${PODS_ROOT}/Headers/Public/RNCClipboard\"",
					"\"${PODS_ROOT}/Headers/Public/RNDeviceInfo\"",
					"\"${PODS_ROOT}/Headers/Public/RNExitApp\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBAnalytics\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBApp\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBMessaging\"",
					"\"${PODS_ROOT}/Headers/Public/RNFS\"",
					"\"${PODS_ROOT}/Headers/Public/RNGestureHandler\"",
					"\"${PODS_ROOT}/Headers/Public/RNInAppBrowser\"",
					"\"${PODS_ROOT}/Headers/Public/RNOS\"",
					"\"${PODS_ROOT}/Headers/Public/RNPermissions\"",
					"\"${PODS_ROOT}/Headers/Public/RNReactNativeHapticFeedback\"",
					"\"${PODS_ROOT}/Headers/Public/RNReanimated\"",
					"\"${PODS_ROOT}/Headers/Public/RNSVG\"",
					"\"${PODS_ROOT}/Headers/Public/RNScreens\"",
					"\"${PODS_ROOT}/Headers/Public/RNSentry\"",
					"\"${PODS_ROOT}/Headers/Public/RNVectorIcons\"",
					"\"${PODS_ROOT}/Headers/Public/React-Core\"",
					"\"${PODS_ROOT}/Headers/Public/React-Fabric\"",
					"\"${PODS_ROOT}/Headers/Public/React-FabricComponents\"",
					"\"${PODS_ROOT}/Headers/Public/React-FabricImage\"",
					"\"${PODS_ROOT}/Headers/Public/React-ImageManager\"",
					"\"${PODS_ROOT}/Headers/Public/React-Mapbuffer\"",
					"\"${PODS_ROOT}/Headers/Public/React-NativeModulesApple\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTAnimation\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTAppDelegate\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTBlob\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTFabric\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTText\"",
					"\"${PODS_ROOT}/Headers/Public/React-RuntimeApple\"",
					"\"${PODS_ROOT}/Headers/Public/React-RuntimeCore\"",
					"\"${PODS_ROOT}/Headers/Public/React-callinvoker\"",
					"\"${PODS_ROOT}/Headers/Public/React-cxxreact\"",
					"\"${PODS_ROOT}/Headers/Public/React-debug\"",
					"\"${PODS_ROOT}/Headers/Public/React-defaultsnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-domnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-featureflags\"",
					"\"${PODS_ROOT}/Headers/Public/React-featureflagsnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-graphics\"",
					"\"${PODS_ROOT}/Headers/Public/React-idlecallbacksnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsc\"",
					"\"${PODS_ROOT}/Headers/Public/React-jserrorhandler\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsi\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsiexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsinspector\"",
					"\"${PODS_ROOT}/Headers/Public/React-logger\"",
					"\"${PODS_ROOT}/Headers/Public/React-microtasksnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-nativeconfig\"",
					"\"${PODS_ROOT}/Headers/Public/React-perflogger\"",
					"\"${PODS_ROOT}/Headers/Public/React-performancetimeline\"",
					"\"${PODS_ROOT}/Headers/Public/React-rendererconsistency\"",
					"\"${PODS_ROOT}/Headers/Public/React-rendererdebug\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimeexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimescheduler\"",
					"\"${PODS_ROOT}/Headers/Public/React-timing\"",
					"\"${PODS_ROOT}/Headers/Public/React-utils\"",
					"\"${PODS_ROOT}/Headers/Public/ReactCodegen\"",
					"\"${PODS_ROOT}/Headers/Public/ReactCommon\"",
					"\"${PODS_ROOT}/Headers/Public/ReactNativeCameraKit\"",
					"\"${PODS_ROOT}/Headers/Public/ReactNativeExceptionHandler\"",
					"\"${PODS_ROOT}/Headers/Public/Sentry\"",
					"\"${PODS_ROOT}/Headers/Public/SocketRocket\"",
					"\"${PODS_ROOT}/Headers/Public/Yoga\"",
					"\"${PODS_ROOT}/Headers/Public/boost\"",
					"\"${PODS_ROOT}/Headers/Public/breez_sdk\"",
					"\"${PODS_ROOT}/Headers/Public/fmt\"",
					"\"${PODS_ROOT}/Headers/Public/glog\"",
					"\"${PODS_ROOT}/Headers/Public/lottie-react-native\"",
					"\"${PODS_ROOT}/Headers/Public/nanopb\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-biometrics\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-blur\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-get-random-values\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-netinfo\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-onesignal\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-randombytes\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-safe-area-context\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-webview\"",
					"\"$(PODS_ROOT)/DoubleConversion\"",
					"$(inherited)",
					"${PODS_ROOT}/Firebase/CoreOnly/Sources",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/boost-for-react-native\"",
					"\"$(PODS_ROOT)/glog\"",
					"\"$(PODS_ROOT)/RCT-Folly\"",
					"\"$(PODS_ROOT)/Headers/Public/React-hermes\"",
					"\"$(PODS_ROOT)/Headers/Public/hermes-engine\"",
					"\"$(PODS_ROOT)/../../node_modules/react-native/ReactCommon\"",
					"\"$(PODS_ROOT)/Headers/Private/React-Core\"",
					"\"$(PODS_ROOT)/../libmpc_core_bindings.h\"",
				);
				INFOPLIST_FILE = "AssetifyApp copy-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = Loans;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.11;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = net.assetify.assetifyLoans;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OBJC_BRIDGING_HEADER = "AssetifyApp-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		14A47EFB2D413D9F00A506E9 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C61BADD5FB8908CE852A7AD2 /* Pods-AssetifyLoans.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = AssetifyApp/AssetifyApp.entitlements;
				CURRENT_PROJECT_VERSION = 130100011;
				DEVELOPMENT_TEAM = 3267WLJ7GG;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/BVLinearGradient\"",
					"\"${PODS_ROOT}/Headers/Public/DoubleConversion\"",
					"\"${PODS_ROOT}/Headers/Public/FBLazyVector\"",
					"\"${PODS_ROOT}/Headers/Public/Firebase\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseCore\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseCoreExtension\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseInstallations\"",
					"\"${PODS_ROOT}/Headers/Public/FirebaseMessaging\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleDataTransport\"",
					"\"${PODS_ROOT}/Headers/Public/GoogleUtilities\"",
					"\"${PODS_ROOT}/Headers/Public/Permission-Camera\"",
					"\"${PODS_ROOT}/Headers/Public/PromisesObjC\"",
					"\"${PODS_ROOT}/Headers/Public/RCT-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/RCTDeprecation\"",
					"\"${PODS_ROOT}/Headers/Public/RCTRequired\"",
					"\"${PODS_ROOT}/Headers/Public/RCTTypeSafety\"",
					"\"${PODS_ROOT}/Headers/Public/RNCAsyncStorage\"",
					"\"${PODS_ROOT}/Headers/Public/RNCClipboard\"",
					"\"${PODS_ROOT}/Headers/Public/RNDeviceInfo\"",
					"\"${PODS_ROOT}/Headers/Public/RNExitApp\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBAnalytics\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBApp\"",
					"\"${PODS_ROOT}/Headers/Public/RNFBMessaging\"",
					"\"${PODS_ROOT}/Headers/Public/RNFS\"",
					"\"${PODS_ROOT}/Headers/Public/RNGestureHandler\"",
					"\"${PODS_ROOT}/Headers/Public/RNInAppBrowser\"",
					"\"${PODS_ROOT}/Headers/Public/RNOS\"",
					"\"${PODS_ROOT}/Headers/Public/RNPermissions\"",
					"\"${PODS_ROOT}/Headers/Public/RNReactNativeHapticFeedback\"",
					"\"${PODS_ROOT}/Headers/Public/RNReanimated\"",
					"\"${PODS_ROOT}/Headers/Public/RNSVG\"",
					"\"${PODS_ROOT}/Headers/Public/RNScreens\"",
					"\"${PODS_ROOT}/Headers/Public/RNSentry\"",
					"\"${PODS_ROOT}/Headers/Public/RNVectorIcons\"",
					"\"${PODS_ROOT}/Headers/Public/React-Core\"",
					"\"${PODS_ROOT}/Headers/Public/React-Fabric\"",
					"\"${PODS_ROOT}/Headers/Public/React-FabricComponents\"",
					"\"${PODS_ROOT}/Headers/Public/React-FabricImage\"",
					"\"${PODS_ROOT}/Headers/Public/React-ImageManager\"",
					"\"${PODS_ROOT}/Headers/Public/React-Mapbuffer\"",
					"\"${PODS_ROOT}/Headers/Public/React-NativeModulesApple\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTAnimation\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTAppDelegate\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTBlob\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTFabric\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTText\"",
					"\"${PODS_ROOT}/Headers/Public/React-RuntimeApple\"",
					"\"${PODS_ROOT}/Headers/Public/React-RuntimeCore\"",
					"\"${PODS_ROOT}/Headers/Public/React-callinvoker\"",
					"\"${PODS_ROOT}/Headers/Public/React-cxxreact\"",
					"\"${PODS_ROOT}/Headers/Public/React-debug\"",
					"\"${PODS_ROOT}/Headers/Public/React-defaultsnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-domnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-featureflags\"",
					"\"${PODS_ROOT}/Headers/Public/React-featureflagsnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-graphics\"",
					"\"${PODS_ROOT}/Headers/Public/React-idlecallbacksnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsc\"",
					"\"${PODS_ROOT}/Headers/Public/React-jserrorhandler\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsi\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsiexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsinspector\"",
					"\"${PODS_ROOT}/Headers/Public/React-logger\"",
					"\"${PODS_ROOT}/Headers/Public/React-microtasksnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-nativeconfig\"",
					"\"${PODS_ROOT}/Headers/Public/React-perflogger\"",
					"\"${PODS_ROOT}/Headers/Public/React-performancetimeline\"",
					"\"${PODS_ROOT}/Headers/Public/React-rendererconsistency\"",
					"\"${PODS_ROOT}/Headers/Public/React-rendererdebug\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimeexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimescheduler\"",
					"\"${PODS_ROOT}/Headers/Public/React-timing\"",
					"\"${PODS_ROOT}/Headers/Public/React-utils\"",
					"\"${PODS_ROOT}/Headers/Public/ReactCodegen\"",
					"\"${PODS_ROOT}/Headers/Public/ReactCommon\"",
					"\"${PODS_ROOT}/Headers/Public/ReactNativeCameraKit\"",
					"\"${PODS_ROOT}/Headers/Public/ReactNativeExceptionHandler\"",
					"\"${PODS_ROOT}/Headers/Public/Sentry\"",
					"\"${PODS_ROOT}/Headers/Public/SocketRocket\"",
					"\"${PODS_ROOT}/Headers/Public/Yoga\"",
					"\"${PODS_ROOT}/Headers/Public/boost\"",
					"\"${PODS_ROOT}/Headers/Public/breez_sdk\"",
					"\"${PODS_ROOT}/Headers/Public/fmt\"",
					"\"${PODS_ROOT}/Headers/Public/glog\"",
					"\"${PODS_ROOT}/Headers/Public/lottie-react-native\"",
					"\"${PODS_ROOT}/Headers/Public/nanopb\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-biometrics\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-blur\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-get-random-values\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-netinfo\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-onesignal\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-randombytes\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-safe-area-context\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-webview\"",
					"\"$(PODS_ROOT)/DoubleConversion\"",
					"$(inherited)",
					"${PODS_ROOT}/Firebase/CoreOnly/Sources",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/boost-for-react-native\"",
					"\"$(PODS_ROOT)/glog\"",
					"\"$(PODS_ROOT)/RCT-Folly\"",
					"\"$(PODS_ROOT)/Headers/Public/React-hermes\"",
					"\"$(PODS_ROOT)/Headers/Public/hermes-engine\"",
					"\"$(PODS_ROOT)/../../node_modules/react-native/ReactCommon\"",
					"\"$(PODS_ROOT)/Headers/Private/React-Core\"",
					"\"$(PODS_ROOT)/../libmpc_core_bindings.h\"",
				);
				INFOPLIST_FILE = "AssetifyApp copy-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = Loans;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.11;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = net.assetify.assetifyLoans;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OBJC_BRIDGING_HEADER = "AssetifyApp-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CXX = "";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
				);
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) DEBUG";
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				CXX = "";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CFLAGS = "$(inherited)";
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
				);
				OTHER_LDFLAGS = "$(inherited)";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "AssetifyAppTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00E356F61AD99517003FC87E /* Debug */,
				00E356F71AD99517003FC87E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "AssetifyApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		14A47EF92D413D9F00A506E9 /* Build configuration list for PBXNativeTarget "AssetifyLoans" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				14A47EFA2D413D9F00A506E9 /* Debug */,
				14A47EFB2D413D9F00A506E9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "AssetifyApp" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		14A47EDC2D413D9F00A506E9 /* XCRemoteSwiftPackageReference "wallet-core" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/trustwallet/wallet-core";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 4.0.32;
			};
		};
		FD36FFC72BBED859004C827C /* XCRemoteSwiftPackageReference "wallet-core" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/trustwallet/wallet-core";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 4.0.32;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		14A47EDB2D413D9F00A506E9 /* SwiftProtobuf */ = {
			isa = XCSwiftPackageProductDependency;
			package = 14A47EDC2D413D9F00A506E9 /* XCRemoteSwiftPackageReference "wallet-core" */;
			productName = SwiftProtobuf;
		};
		14A47EDD2D413D9F00A506E9 /* WalletCore */ = {
			isa = XCSwiftPackageProductDependency;
			package = 14A47EDC2D413D9F00A506E9 /* XCRemoteSwiftPackageReference "wallet-core" */;
			productName = WalletCore;
		};
		FD36FFC82BBED859004C827C /* SwiftProtobuf */ = {
			isa = XCSwiftPackageProductDependency;
			package = FD36FFC72BBED859004C827C /* XCRemoteSwiftPackageReference "wallet-core" */;
			productName = SwiftProtobuf;
		};
		FD36FFCA2BBED859004C827C /* WalletCore */ = {
			isa = XCSwiftPackageProductDependency;
			package = FD36FFC72BBED859004C827C /* XCRemoteSwiftPackageReference "wallet-core" */;
			productName = WalletCore;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
