import {memo, useCallback, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {KeyboardAvoidingView, Platform, StyleSheet, TextInput, View} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';
import {useFocusInput} from '@/hooks/keyboard';
import {useAppDispatch} from '@/hooks/redux';
import WalletService from '@/services/WalletService';
import {setIsLoggedIn, setUser, setUserAddresses} from '@/storage/actions/authActions';
import theme from '@/styles/theme';
import {AuthAddresses} from '@/types/authTypes';
import {enableNotifications} from '@/utils';
import {showWarningToast} from '@/utils/toast';
import {getKeychainValue} from '@/utils/keychain';
import MButton from '@components/MButton';
import {PasswordInput} from '@components/PasswordInput';
import {Caption, Footer, Subheading} from '@styles/styled-components';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {getBitcoinWallet, getExistingWallets} from '../../helpers/import-wallet-helpers';

const RestoreRecoveryAndImport = ({route}: {route: any}) => {
  const recoveryId = route.params?.recoveryId;

  const {t} = useTranslation();
  const dispatch = useAppDispatch();
  const inputRef = useRef<TextInput>(null);

  useFocusInput(inputRef);

  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);

  const handleRestoreWallet = useCallback(async () => {
    if (!recoveryId) return;

    setLoading(true);

    setTimeout(async () => {
      try {
        const result = await getKeychainValue(recoveryId, password);

        if (!result) {
          showWarningToast('Incorrect Password');
          setLoading(false);
          return;
        }

        const seedPhrase = result.password;

        if (!seedPhrase) {
          console.error('[RestoreRecoveryAndImport] No seed phrase found');
          showWarningToast('There was an error restoring your wallet. Please try again');
          setLoading(false);
          return;
        }

        const walletService = new WalletService();

        const {wallet, address}: any = await getBitcoinWallet(seedPhrase);

        const bitcoinChains = await walletService.walletLoggerBTC(
          address.address,
          wallet.xPubsList && wallet.xPubsList[0] ? wallet.xPubsList[0].accountXpub : '',
        );

        if (bitcoinChains.status === 409) {
          const {wallets, addresses}: any = await getExistingWallets(wallet, address);

          dispatch(setUserAddresses(addresses));
          dispatch(
            setUser({
              wallet: wallets,
              pinCode: '',
            }),
          );

          if (addresses && addresses.length > 0) {
            const firstAddress = addresses[0];
            if (firstAddress) {
              const addressValue =
                'addresses' in firstAddress
                  ? firstAddress.addresses[0]?.address
                  : firstAddress.address;

              if (addressValue) {
                await enableNotifications(addressValue);
              }
            }
          }
          dispatch(setIsLoggedIn());
        } else {
          const createdWallets = await walletService.createWallets(seedPhrase);
          const createdAddresses = await walletService.createAddresses(createdWallets!);

          const {wallets, addresses, loggerRequest} =
            await walletService.createLoggerRequest(
              createdWallets!,
              createdAddresses as AuthAddresses,
            );

          await walletService.walletLogger(loggerRequest);

          dispatch(setUserAddresses(addresses));
          dispatch(setUser({wallet: wallets, pinCode: ''}));

          if (addresses && addresses.length > 0) {
            const firstAddress = addresses[0];
            if (firstAddress) {
              const addressValue =
                'addresses' in firstAddress
                  ? firstAddress.addresses[0]?.address
                  : firstAddress.address;

              if (addressValue) {
                await enableNotifications(addressValue);
              }
            }
          }
          dispatch(setIsLoggedIn());
        }
      } catch (e) {
        setLoading(false);
        showWarningToast('There was an error restoring your wallet. Please try again');
      }
    }, 300);
  }, [recoveryId, dispatch, password]);

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? (theme.isSmallDevice ? 60 : 90) : 0}
      style={styles.keyboardAvoidingView}
    >
      <KeyboardAwareScrollView
        style={styles.scrollView}
        keyboardShouldPersistTaps="handled"
        enableAutomaticScroll={false}
        enableOnAndroid
      >
        <View style={styles.content}>
          <Caption style={styles.caption}>{t('wallet.restoreWallet')}</Caption>

          <Subheading style={styles.description}>
            {t('wallet.restoreWalletAndImport')}
          </Subheading>

          <PasswordInput
            ref={inputRef}
            label="Password"
            value={password}
            onChangeText={setPassword}
            placeholder="Enter your password"
            textContentType="none"
          />
        </View>
      </KeyboardAwareScrollView>

      <Footer style={styles.footer}>
        <MButton
          text={t('wallet.restoreWallet')}
          onPress={handleRestoreWallet}
          disabled={!password}
          isLoading={loading}
          loadingText="Restoring Wallet"
        />
      </Footer>
    </KeyboardAvoidingView>
  );
};

export default memo(RestoreRecoveryAndImport);

const styles = StyleSheet.create({
  keyboardAvoidingView: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: theme.layout.ph.screen,
    paddingVertical: theme.layout.pv.screen * 2,
    justifyContent: 'flex-start',
    gap: theme.spacing.lg,
  },
  caption: {
    textAlign: 'center',
  },
  description: {
    textAlign: 'center',
  },
  footer: {
    paddingHorizontal: theme.layout.ph.screen * 2,
  },
});
