import React, {useCallback, useMemo, useState} from 'react';
import {StyleSheet, Text, View} from 'react-native';

import {BaseOption} from '@/components/BottomSheetList';
import Checkbox from '@/components/Checkbox';
import MButton from '@/components/MButton';
import RadioButton from '@/components/RadioButton';
import SelectOption from '@/components/SelectOption';
import {TextInput} from '@/components/TextInput';
import GlobalStyles from '@/constants/GlobalStyles';
import {useBottomSheet} from '@/hooks/bottomSheet';
import {useKyc} from '@/screens/Loan/components/kyc/KycContext';
import theme from '@/styles/theme';
import {PERSONAL_INFORMATION_DEFAULT_VALUES} from './utils';

const industryOptions: BaseOption[] = [
  {label: 'Technology & Communications', value: 'technology'},
  {label: 'Finance & Banking', value: 'finance'},
  {label: 'Healthcare', value: 'healthcare'},
];

const sectorOptions: BaseOption[] = [
  {label: 'IT Services', value: 'itServices'},
  {label: 'Software Development', value: 'softwareDevelopment'},
  {label: 'Telecommunications', value: 'telecommunications'},
];

const countryOptions: BaseOption[] = [
  {label: 'Bulgaria', value: 'bg'},
  {label: 'United Kingdom', value: 'uk'},
  {label: 'Australia', value: 'au'},
  {label: 'Germany', value: 'de'},
];

interface PersonalInfoFormProps {
  onNext: () => void;
}

const KYC_DEFAULT_SNAP_POINTS = [40];

const PersonalInfoForm: React.FC<PersonalInfoFormProps> = ({onNext}) => {
  const {kycState, updatePersonalInformation, goToNextStep} = useKyc();
  const {open: openSheet, close: closeSheet} = useBottomSheet('list');

  // Initialize form state from context if available
  const storedPersonalInfo =
    kycState.personalInformation || PERSONAL_INFORMATION_DEFAULT_VALUES;

  // Form state
  const [firstName, setFirstName] = useState(storedPersonalInfo.firstName || '');
  const [lastName, setLastName] = useState(storedPersonalInfo.lastName || '');
  const [dateOfBirth, setDateOfBirth] = useState(storedPersonalInfo.dateOfBirth || '');
  const [phoneNumber, setPhoneNumber] = useState(storedPersonalInfo.phoneNumber || '');
  const [occupation, setOccupation] = useState(storedPersonalInfo.occupation || '');

  const [selectedIndustry, setSelectedIndustry] = useState<BaseOption | null>(
    storedPersonalInfo.occupationIndustry
      ? industryOptions.find(
          (option) => option.value === storedPersonalInfo.occupationIndustry,
        ) || null
      : null,
  );

  const [selectedSector, setSelectedSector] = useState<BaseOption | null>(
    storedPersonalInfo.occupationSector
      ? sectorOptions.find(
          (option) => option.value === storedPersonalInfo.occupationSector,
        ) || null
      : null,
  );

  const [selectedCountry, setSelectedCountry] = useState<BaseOption | null>(
    storedPersonalInfo.citizenship && storedPersonalInfo.citizenship.length > 0
      ? countryOptions.find(
          (option) => option.value === storedPersonalInfo.citizenship[0],
        ) || null
      : null,
  );

  const [isPep, setIsPep] = useState<boolean>(storedPersonalInfo.isPep || false);

  const [confirmNoThirdParty, setConfirmNoThirdParty] = useState(
    storedPersonalInfo.confirmNoThirdParty || false,
  );

  const handleContinue = () => {
    // Save personal information to context
    updatePersonalInformation({
      firstName,
      lastName,
      dateOfBirth,
      phoneNumber,
      occupation,
      occupationIndustry: selectedIndustry?.value || '',
      occupationSector: selectedSector?.value || '',
      citizenship: selectedCountry ? [selectedCountry.value] : [],
      isPep,
      confirmNoThirdParty,
    });

    // Update step in the KYC context
    goToNextStep();

    // Navigate to the next step
    onNext();
  };

  // Memoize form validation to prevent unnecessary recalculations
  const isFormValid = useMemo(() => {
    return !!(
      firstName.trim() &&
      lastName.trim() &&
      dateOfBirth.trim() &&
      phoneNumber.trim() &&
      occupation.trim() &&
      selectedIndustry &&
      selectedSector &&
      selectedCountry &&
      confirmNoThirdParty
    );
  }, [
    firstName,
    lastName,
    dateOfBirth,
    phoneNumber,
    occupation,
    selectedIndustry,
    selectedSector,
    selectedCountry,
    confirmNoThirdParty,
  ]);

  const handleOpenIndustrySheet = useCallback(() => {
    openSheet(
      {
        type: 'list' as const,
        data: industryOptions,
        onSelect: (item) => {
          setSelectedIndustry(item as BaseOption);
          closeSheet();
        },
      },
      KYC_DEFAULT_SNAP_POINTS,
      false,
    );
  }, [openSheet, closeSheet]);

  const handleOpenSectorSheet = useCallback(() => {
    openSheet(
      {
        type: 'list' as const,
        data: sectorOptions,
        onSelect: (item) => {
          setSelectedSector(item as BaseOption);
          closeSheet();
        },
      },
      KYC_DEFAULT_SNAP_POINTS,
      false,
    );
  }, [openSheet, closeSheet]);

  const handleOpenCountrySheet = useCallback(() => {
    openSheet(
      {
        type: 'list' as const,
        data: countryOptions,
        onSelect: (item) => {
          setSelectedCountry(item as BaseOption);
          closeSheet();
        },
      },
      KYC_DEFAULT_SNAP_POINTS,
      false,
    );
  }, [openSheet, closeSheet]);

  return (
    <>
      <Text style={styles.formDescription}>
        The following questions will help Assetify provide you with the best possible
        experience and ensure your account is compliant with applicable laws and
        regulations.
      </Text>

      <TextInput
        label="Legal first name"
        value={firstName}
        onChangeText={setFirstName}
        placeholder="John"
      />

      <View style={styles.inputSpacing} />

      <TextInput
        label="Legal last name"
        value={lastName}
        onChangeText={setLastName}
        placeholder="Doe"
      />

      <View style={styles.inputSpacing} />

      <TextInput
        label="Date of Birth"
        value={dateOfBirth}
        onChangeText={setDateOfBirth}
        placeholder="MM/DD/YYYY"
      />

      <View style={styles.inputSpacing} />

      <TextInput
        label="Phone Number"
        value={phoneNumber}
        onChangeText={setPhoneNumber}
        keyboardType="phone-pad"
        placeholder="+359 (88) 888-8888"
      />

      <View style={styles.inputSpacing} />

      <TextInput
        label="Occupation"
        value={occupation}
        onChangeText={setOccupation}
        placeholder="MD"
      />

      <View style={styles.inputSpacing} />

      <SelectOption
        label="Occupation Industry"
        option={selectedIndustry?.label || 'Select an industry'}
        onPress={handleOpenIndustrySheet}
      />

      <View style={styles.inputSpacing} />

      <SelectOption
        label="Occupation sector"
        option={selectedSector?.label || 'Select a sector'}
        onPress={handleOpenSectorSheet}
      />

      <View style={styles.inputSpacing} />

      <SelectOption
        label="Citizenship"
        option={selectedCountry?.label || 'Select a country'}
        onPress={handleOpenCountrySheet}
      />

      <View style={styles.inputSpacing} />

      {/* PEP Question */}
      <View style={styles.pepContainer}>
        <Text style={styles.pepQuestion}>
          Are you, or a close relative, or a close associate, a Domestic/Foreign
          Politically Exposed Person or Head of an International Organization?
        </Text>

        <View style={styles.radioGroup}>
          <RadioButton
            label="No, I am not"
            selected={isPep === false}
            onPress={() => setIsPep(false)}
          />

          <RadioButton
            label="Yes, I am"
            selected={isPep === true}
            onPress={() => setIsPep(true)}
          />
        </View>
      </View>

      {/* Confirmation Checkbox */}
      <Checkbox
        label="I confirm that the account will not be used to conduct a transaction or activity for or on behalf of another person or entity."
        checked={confirmNoThirdParty}
        onPress={() => setConfirmNoThirdParty(!confirmNoThirdParty)}
      />

      {/* Continue Button */}
      <View style={{marginTop: theme.spacing.xxl}}>
        <MButton text="Continue" onPress={handleContinue} disabled={!isFormValid} />
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  formDescription: {
    fontSize: 16,
    lineHeight: 24,
    color: GlobalStyles.gray.gray800,
    marginBottom: 24,
    fontFamily: GlobalStyles.fonts.sfPro,
  },
  inputSpacing: {
    height: 16,
  },
  pepContainer: {
    marginTop: 16,
    marginBottom: 16,
  },
  pepQuestion: {
    fontSize: 16,
    lineHeight: 24,
    color: GlobalStyles.base.black,
    marginBottom: 16,
    fontFamily: GlobalStyles.fonts.sfPro,
  },
  radioGroup: {
    marginTop: 8,
  },
  disabledButton: {
    backgroundColor: GlobalStyles.gray.gray600,
  },
});

export default PersonalInfoForm;
