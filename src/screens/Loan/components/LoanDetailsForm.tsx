import {StyleSheet, Text, View} from 'react-native';

import type {CurrencyOption} from '@/components/BottomSheetList';
import CurrencyInput from '@/components/CurrencyInput';
import SelectOption from '@/components/SelectOption';
import GlobalStyles from '@/constants/GlobalStyles';
import {
  calculateMaxLoanAmount,
  calculateRequiredCollateral,
  validateAmount,
} from '../utils/loan-calculations';
import {LoanDetailsFormProps} from '../utils/loan-types';
import {
  CURRENCY_OPTIONS,
  INTEREST_OPTIONS,
  LTV_OPTIONS,
  TERM_OPTIONS,
} from '../screens/apply-to-a-loan/utils';

const LoanDetailsForm = ({
  onOpenBottomSheet,
  setLoanData,
  loanData,
  getTokenPrice,
}: LoanDetailsFormProps) => {
  const handleBorrowAmountChange = (value: string) => {
    setLoanData((prev) => ({
      ...prev,
      borrowAmount: value,
    }));
  };

  const handleBorrowAmountBlur = () => {
    const value = loanData.borrowAmount;
    if (!validateAmount(value)) {
      setLoanData((prev) => ({
        ...prev,
        errors: {
          ...prev.errors,
          borrowAmount: 'Please enter a valid amount',
        },
      }));
      return;
    }

    const tokenPrice = getTokenPrice(loanData.collateralCurrency.label);
    const borrowValue = parseFloat(value);
    const ltvRatio = parseFloat(loanData.loanToValue) / 100;

    const requiredCollateral = calculateRequiredCollateral(
      borrowValue,
      tokenPrice,
      ltvRatio,
      loanData.borrowCurrency.label,
    );

    setLoanData((prev) => ({
      ...prev,
      collateralAmount: requiredCollateral,
    }));

    setLoanData((prev) => ({
      ...prev,
      errors: {
        ...prev.errors,
        borrowAmount: undefined,
        collateralAmount: undefined,
      },
    }));
  };

  const handleCollateralAmountChange = (value: string) => {
    setLoanData((prev) => ({
      ...prev,
      collateralAmount: value.replace(/,/g, '.'),
    }));
  };

  const handleCollateralAmountBlur = () => {
    const value = loanData.collateralAmount;
    if (!validateAmount(value)) {
      setLoanData((prev) => ({
        ...prev,
        errors: {
          ...prev.errors,
          collateralAmount: 'Please enter a valid amount',
        },
      }));
      return;
    }

    const tokenPrice = getTokenPrice(loanData.collateralCurrency.label);
    const collateralValue = parseFloat(value);
    const ltvRatio = parseFloat(loanData.loanToValue) / 100;

    const maxLoan = calculateMaxLoanAmount(
      collateralValue,
      tokenPrice,
      ltvRatio,
      loanData.borrowCurrency.label,
    );

    setLoanData((prev) => ({
      ...prev,
      borrowAmount: maxLoan,
    }));

    setLoanData((prev) => ({
      ...prev,
      errors: {
        ...prev.errors,
        collateralAmount: undefined,
        borrowAmount: undefined,
      },
    }));
  };

  return (
    <View style={{alignItems: 'center'}}>
      <View style={styles.inputContainer}>
        <CurrencyInput
          label="I want to borrow"
          value={loanData.borrowAmount}
          onChangeText={handleBorrowAmountChange}
          error={loanData.errors.borrowAmount}
          selectedOption={loanData.borrowCurrency as CurrencyOption}
          onBlur={handleBorrowAmountBlur}
          onOptionPress={() =>
            onOpenBottomSheet({
              type: 'currency',
              data: CURRENCY_OPTIONS.fiat,
              title: 'Select Currency',
            })
          }
          decimals={0}
        />

        <CurrencyInput
          label="Using as collateral"
          value={loanData.collateralAmount}
          onChangeText={handleCollateralAmountChange}
          onBlur={handleCollateralAmountBlur}
          error={loanData.errors.collateralAmount}
          selectedOption={loanData.collateralCurrency as CurrencyOption}
          onOptionPress={() =>
            onOpenBottomSheet({
              type: 'currency',
              data: CURRENCY_OPTIONS.crypto,
              title: 'Select Currency',
            })
          }
        />

        <View style={styles.loanParametersContainer}>
          <Text style={styles.sectionTitle}>Loan Parameters</Text>

          <SelectOption
            label="Loan-To-Value (LTV)"
            option={loanData.loanToValue}
            style={styles.selectContainer}
            onPress={() => {
              onOpenBottomSheet({
                type: 'simple',
                data: LTV_OPTIONS,
                title: 'Select LTV',
              });
            }}
          />

          <SelectOption
            label="Interest Payment"
            option={loanData.interestPayment}
            style={styles.selectContainer}
            onPress={() => {
              onOpenBottomSheet({
                type: 'simple',
                data: INTEREST_OPTIONS,
                title: 'Select Interest Payment',
              });
            }}
          />

          <SelectOption
            label="Term"
            option={loanData.term}
            style={styles.selectContainer}
            onPress={() => {
              onOpenBottomSheet({
                type: 'simple',
                data: TERM_OPTIONS,
                title: 'Select Term',
              });
            }}
          />
        </View>
      </View>
    </View>
  );
};

export default LoanDetailsForm;

const styles = StyleSheet.create({
  inputContainer: {
    marginTop: 32,
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '95%',
    alignSelf: 'center',
    paddingHorizontal: 12,
    gap: 18,
  },
  coloredInputContainer: {
    backgroundColor: GlobalStyles.primary.primary50,
  },
  loanParametersContainer: {
    justifyContent: 'flex-start',
    width: '100%',
    paddingHorizontal: 16,
    // backgroundColor: GlobalStyles.gray.gray100,
    borderWidth: 1,
    borderColor: GlobalStyles.gray.gray600,
    borderRadius: 12,
    paddingVertical: 10,
    marginTop: 16,
  },
  sectionTitle: {
    fontSize: 18,
    color: GlobalStyles.gray.gray900,
    fontWeight: '400',
    marginTop: 10,
    marginBottom: 16,
  },
  selectContainer: {
    marginBottom: 12,
    borderWidth: 1,
    borderColor: GlobalStyles.gray.gray600,
    borderRadius: 8,
    backgroundColor: GlobalStyles.base.white,
  },
});
