import {memo} from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {ClipboardIcon} from 'react-native-heroicons/outline';

import GlobalStyles from '@/constants/GlobalStyles';
import theme from '@/styles/theme';
import {handleCopy} from '@/utils';
import {Loan} from '../utils/loan-types';
import {getLoanStatusText, getStatusColor, isActive, isPending} from './utils';

type Props = {
  loan: Loan;
  onPress?: (loanId: string) => void;
  showDepositAddress?: boolean;
};

const LoanCard = memo(({loan, onPress, showDepositAddress = false}: Props) => {
  const formattedDate = new Date(loan.createdAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });

  const statusColors = getStatusColor(loan.state);
  const statusText = getLoanStatusText(loan.state);

  const handleCopyAddress = () => {
    if (loan.terms.depositAddress) {
      handleCopy(loan.terms.depositAddress);
    }
  };

  const cardContent = (
    <>
      {/* Status Badge */}
      <View style={[styles.statusBadge, {backgroundColor: statusColors.badge}]}>
        <Text style={styles.statusBadgeText}>{statusText}</Text>
      </View>

      <View style={styles.loanHeader}>
        <Text style={styles.loanReference}>
          {isPending(loan.state) ? 'Loan Reference' : getLoanStatusText(loan.state)}
        </Text>
        <Text style={styles.loanDate}>{formattedDate}</Text>
      </View>

      <View style={styles.loanAmount}>
        <Text style={styles.amountLabel}>Principal Amount</Text>
        <Text style={styles.amountValue}>
          {loan.terms.fiat.amount.toLocaleString()} {loan.terms.fiat.currency}
        </Text>
      </View>

      <View style={styles.divider} />

      <View style={styles.loanDetails}>
        <Text style={styles.sectionTitle}>Loan Details</Text>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Collateral</Text>
          <Text style={styles.detailValue}>
            {loan.terms.collateral.amount} {loan.terms.collateral.currency}
          </Text>
        </View>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>LTV</Text>
          <Text style={styles.detailValue}>{loan.terms.ltv}%</Text>
        </View>
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Term</Text>
          <Text style={styles.detailValue}>{loan.terms.term} months</Text>
        </View>
      </View>

      {showDepositAddress && loan.terms.depositAddress && (
        <View style={styles.depositSection}>
          <Text style={styles.sectionTitle}>Deposit Information</Text>
          <View style={styles.depositAddressContainer}>
            <View style={styles.depositAddressContent}>
              <Text style={styles.depositAddressLabel}>Deposit Address</Text>
              <View style={styles.depositAddressRow}>
                <Text
                  style={styles.depositAddress}
                  numberOfLines={1}
                  ellipsizeMode="middle"
                >
                  {loan.terms.depositAddress}
                </Text>
                <TouchableOpacity
                  onPress={handleCopyAddress}
                  accessibilityLabel="Copy address"
                >
                  <ClipboardIcon color={GlobalStyles.primary.primary500} size={20} />
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      )}

      {isActive(loan.state) && (
        <View style={styles.viewDetails}>
          <Text style={styles.viewDetailsText}>View Details</Text>
        </View>
      )}
    </>
  );

  if (onPress) {
    return (
      <TouchableOpacity style={styles.loanCard} onPress={() => onPress(loan._id)}>
        {cardContent}
      </TouchableOpacity>
    );
  }

  return <View style={styles.loanCard}>{cardContent}</View>;
});

const styles = StyleSheet.create({
  loanCard: {
    backgroundColor: GlobalStyles.base.white,
    borderRadius: 12,
    marginBottom: 16,
    padding: 16,
    shadowColor: GlobalStyles.base.black,
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
    position: 'relative',
  },
  statusBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: GlobalStyles.primary.primary300,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderTopRightRadius: 12,
    borderBottomLeftRadius: 12,
    zIndex: 1,
    opacity: 0.8,
  },
  statusBadgeText: {
    color: GlobalStyles.base.white,
    fontSize: 12,
    fontWeight: '700',
    textTransform: 'uppercase',
  },
  loanHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
    marginTop: 8,
  },
  loanReference: {
    fontSize: 14,
    color: GlobalStyles.gray.gray800,
    fontWeight: '600',
  },
  loanDate: {
    fontSize: 12,
    color: GlobalStyles.gray.gray900,
    paddingTop: theme.spacing.sm,
  },
  loanAmount: {
    marginBottom: 16,
  },
  amountLabel: {
    fontSize: 12,
    color: GlobalStyles.gray.gray800,
    marginBottom: 4,
    fontWeight: '500',
  },
  amountValue: {
    fontSize: 24,
    fontWeight: '700',
    color: GlobalStyles.base.black,
    letterSpacing: 0.5,
  },
  divider: {
    height: 1,
    backgroundColor: GlobalStyles.gray.gray300,
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: GlobalStyles.gray.gray800,
    marginBottom: 12,
  },
  loanDetails: {
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 14,
    color: GlobalStyles.gray.gray800,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '600',
    color: GlobalStyles.base.black,
  },
  depositSection: {
    marginBottom: 16,
    backgroundColor: GlobalStyles.gray.gray200,
    padding: 12,
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: GlobalStyles.primary.primary500,
  },
  depositAddressContainer: {
    marginVertical: 8,
    paddingVertical: 10,
    borderRadius: 8,
    backgroundColor: GlobalStyles.base.white,
    borderWidth: 1,
    borderColor: GlobalStyles.gray.gray400,
  },
  depositAddressContent: {
    paddingHorizontal: 12,
  },
  depositAddressLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: GlobalStyles.gray.gray800,
    marginBottom: 4,
    textTransform: 'uppercase',
  },
  depositAddressRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  depositAddress: {
    fontSize: 14,
    color: GlobalStyles.gray.gray900,
    flex: 1,
    marginRight: 8,
    fontFamily: 'monospace',
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: GlobalStyles.gray.gray300,
  },
  indicatorDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: GlobalStyles.orange.orange500,
    marginRight: 8,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
    color: GlobalStyles.primary.primary700,
    letterSpacing: 0.2,
  },
  viewDetails: {
    alignItems: 'center',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: GlobalStyles.gray.gray300,
  },
  viewDetailsText: {
    fontSize: 14,
    fontWeight: '600',
    color: GlobalStyles.primary.primary500,
  },
});

export default LoanCard;
