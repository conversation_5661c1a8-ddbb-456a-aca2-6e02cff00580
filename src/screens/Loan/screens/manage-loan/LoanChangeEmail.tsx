import {useBottomTabBarHeight} from '@react-navigation/bottom-tabs';
import React, {useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {
  KeyboardAwareScrollView,
  KeyboardStickyView,
} from 'react-native-keyboard-controller';

import MButton from '@/components/MButton';
import {PasswordInput} from '@/components/PasswordInput';
import {TextInput} from '@/components/TextInput';
import GlobalStyles from '@/constants/GlobalStyles';
import {Footer} from '@/styles/styled-components';
import theme from '@/styles/theme';

const LoanChangeEmail: React.FC = () => {
  const handleChangeEmail = () => {};

  const [password, setPassword] = useState();

  const height = useBottomTabBarHeight();
  const offset = height + (theme.isSmallDevice ? 32 : 46);

  return (
    <View style={styles.root}>
      <KeyboardAwareScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.content}
        keyboardShouldPersistTaps="handled"
        bottomOffset={140}
      >
        <View style={styles.inputContainer}>
          <TextInput
            label="Current Email"
            editable={false}
            placeholder="<EMAIL>"
            placeholderTextColor={GlobalStyles.gray.gray800}
          />

          <TextInput
            label="New Email"
            placeholder="Enter new email"
            placeholderTextColor={GlobalStyles.gray.gray700}
          />

          <PasswordInput
            label="Your Password"
            value={password}
            onChangeText={setPassword}
            placeholder="Enter your password"
          />
        </View>
      </KeyboardAwareScrollView>

      <KeyboardStickyView offset={{opened: offset}}>
        <Footer>
          <MButton text="Change Email" onPress={handleChangeEmail} />
        </Footer>
      </KeyboardStickyView>
    </View>
  );
};

export default LoanChangeEmail;

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: theme.colors.base.white,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: theme.layout.ph.screen,
    marginTop: theme.layout.pv.lg,
  },
  inputContainer: {
    gap: 22,
  },
});
