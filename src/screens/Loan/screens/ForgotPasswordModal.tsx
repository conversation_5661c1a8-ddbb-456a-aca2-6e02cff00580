import {BottomSheetModal} from '@gorhom/bottom-sheet';
import {useMutation} from '@tanstack/react-query';
import React, {forwardRef, memo, useCallback, useMemo, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {showMessage} from 'react-native-flash-message';
import {EnvelopeIcon} from 'react-native-heroicons/outline';

import MButton from '@/components/MButton';
import {TextInput} from '@/components/TextInput';
import GlobalStyles from '@/constants/GlobalStyles';
import {BodyM} from '@/styles/styled-components';
import theme from '@/styles/theme';
import {showWarningToast} from '@/utils/toast';
import BaseModal from '../../../components/modals/BaseModal';
import {validateEmailWithMessage} from '../utils/loan-helpers';
import {resetPassword} from '../utils/loan-mutations';

type Props = {
  onClose?: () => void;
};

const ForgotPasswordModal = forwardRef<BottomSheetModal, Props>(({onClose}, ref) => {
  const [email, setEmail] = useState<string>('');

  const forgotPasswordMutation = useMutation({
    mutationFn: resetPassword,
  });

  const validateEmail = useMemo(() => {
    if (!email) return {isValid: false, message: undefined};
    return validateEmailWithMessage(email);
  }, [email]);

  const handleSubmit = useCallback(async () => {
    forgotPasswordMutation.mutate(
      {email},
      {
        onSuccess: () => {
          // Show custom success toast matching the design
          showMessage({
            message: 'Check your inbox!',
            description: `A link to reset your password has been sent to\n${email.trim()}`,
            type: 'success',
            backgroundColor: GlobalStyles.base.white,
            color: GlobalStyles.base.black,
            titleStyle: {
              fontSize: 16,
              fontWeight: '600',
              color: GlobalStyles.base.black,
              marginBottom: 4,
            },
            textStyle: {
              fontSize: 14,
              color: GlobalStyles.gray.gray900,
              lineHeight: 18,
            },
            renderFlashMessageIcon: () => (
              <View
                style={{
                  width: 40,
                  height: 40,
                  borderRadius: 20,
                  backgroundColor: GlobalStyles.base.black,
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginRight: 12,
                }}
              >
                <EnvelopeIcon size={20} color={GlobalStyles.base.white} />
              </View>
            ),
            style: {
              borderRadius: 12,
              marginHorizontal: 16,
              marginTop: 50,
              paddingHorizontal: 16,
              paddingVertical: 16,
              shadowColor: GlobalStyles.base.black,
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.1,
              shadowRadius: 8,
              elevation: 4,
            },
            floating: true,
            duration: 4000,
          });

          // Close modal
          if (ref && 'current' in ref) {
            ref.current?.dismiss();
          }
          onClose?.();
        },
        onError: (error) => {
          // Show error toast
          showWarningToast(
            error.message || 'Failed to send reset link. Please try again.',
          );
        },
      },
    );
  }, [email, ref, onClose]);

  const isFormValid = useMemo(() => {
    return validateEmail.isValid && !forgotPasswordMutation.isPending;
  }, [validateEmail.isValid, forgotPasswordMutation.isPending]);

  return (
    <BaseModal ref={ref} snapPoints={['90%']} title="Reset Password">
      <View style={styles.container}>
        <BodyM>
          Enter the email address associated with your account, and we'll email you a link
          to reset your password
        </BodyM>

        <View style={styles.inputContainer}>
          <TextInput
            label="Email"
            value={email}
            onChangeText={setEmail}
            placeholder="Enter your email address"
            autoCorrect={false}
          />
        </View>

        <View style={styles.buttonContainer}>
          <MButton
            text="Send reset link"
            onPress={handleSubmit}
            disabled={!isFormValid}
            isLoading={forgotPasswordMutation.isPending}
            loadingText="Sending Email..."
            variant="primary"
          />
        </View>
      </View>
    </BaseModal>
  );
});

export default memo(ForgotPasswordModal);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: theme.spacing.lg,
    paddingTop: theme.spacing.md,
    gap: 22,
  },
  description: {
    color: GlobalStyles.gray.gray900,
    lineHeight: 22,
    marginBottom: theme.spacing.xl,
    textAlign: 'left',
  },
  inputContainer: {
    marginBottom: theme.spacing.xl,
  },
  emailInput: {
    backgroundColor: GlobalStyles.base.white,
    borderColor: GlobalStyles.gray.gray300,
    borderRadius: 8,
  },
  buttonContainer: {
    marginTop: theme.spacing.md,
  },
});
