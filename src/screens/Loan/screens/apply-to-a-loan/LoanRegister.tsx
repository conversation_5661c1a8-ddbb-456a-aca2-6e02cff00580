import {useBottomTabBarHeight} from '@react-navigation/bottom-tabs';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import React, {memo, useState} from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {
  KeyboardAwareScrollView,
  KeyboardStickyView,
} from 'react-native-keyboard-controller';

import AssetifyLogo from '@/assets/logo/Logo.svg';
import MButton from '@/components/MButton';
import {PasswordInput} from '@/components/PasswordInput';
import {TextInput} from '@/components/TextInput';
import GlobalStyles from '@/constants/GlobalStyles';
import {useAppDispatch} from '@/hooks/redux';
import {LoanStackParamList} from '@/navigation/types';
import {AuthenticationInstance} from '@/services/BackendServices';
import {setIsRegistered} from '@/storage/actions/loanActions';
import {Footer} from '@/styles/styled-components';
import {showWarningToast} from '@/utils/toast';
import theme from '@styles/theme';

type LoanRegisterNavigationProp = StackNavigationProp<LoanStackParamList, 'LoanRegister'>;

const LoanRegister: React.FC = () => {
  const navigation = useNavigation<LoanRegisterNavigationProp>();
  const dispatch = useAppDispatch();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [confirmPassword, setConfirmPassword] = useState('');

  const height = useBottomTabBarHeight();
  const offset = height + (theme.isSmallDevice ? 32 : 46);

  const handleSignUp = async () => {
    if (password !== confirmPassword) {
      showWarningToast('Password Mismatch');
      return;
    }

    setLoading(true);

    try {
      const result = await AuthenticationInstance.post(`/user/signup`, {email, password});

      if (result.status === 201 || result.status === 200) {
        setLoading(false);
        navigation.replace('LoanRegistrationSuccess');
        dispatch(setIsRegistered(true));
      }
    } catch (error: any) {
      setLoading(false);
      showWarningToast('Registration Failed');
    }
  };

  return (
    <View style={styles.root}>
      <KeyboardAwareScrollView
        contentContainerStyle={styles.keyboardAware}
        keyboardShouldPersistTaps="handled"
        bottomOffset={140}
      >
        <View style={styles.content}>
          <View style={styles.logoContainer}>
            <AssetifyLogo width={42} height={42} />
            <Text style={styles.logoText}>Register</Text>
          </View>

          <Text style={styles.title}>Create an account</Text>

          <View style={styles.formContainer}>
            <TextInput
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              placeholder="<EMAIL>"
              placeholderTextColor={GlobalStyles.gray.gray700}
              label="Email"
            />

            <PasswordInput
              value={password}
              onChangeText={setPassword}
              label="Password"
              placeholder="••••••••"
              placeholderTextColor={GlobalStyles.gray.gray700}
            />

            <PasswordInput
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              onSubmitEditing={handleSignUp}
              label="Confirm Password"
              placeholder="••••••••"
              placeholderTextColor={GlobalStyles.gray.gray700}
              textContentType="none"
            />
          </View>

          <View style={styles.signInContainer}>
            <Text style={styles.signInText}>Already have an account? </Text>
            <TouchableOpacity onPress={() => navigation.navigate('LoanLogin')}>
              <Text style={styles.signInLink}>Sign In</Text>
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAwareScrollView>

      <KeyboardStickyView offset={{opened: offset}}>
        <Footer>
          <MButton
            text="Create Account"
            onPress={handleSignUp}
            isLoading={loading}
            disabled={
              !email || !password || !confirmPassword || password !== confirmPassword
            }
          />
        </Footer>
      </KeyboardStickyView>
    </View>
  );
};

export default memo(LoanRegister);

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: 'white',
  },
  keyboardAware: {
    flex: 1,
    justifyContent: 'center',
  },
  content: {
    alignItems: 'center',
    paddingHorizontal: theme.layout.ph.screen,
    gap: theme.spacing.lg,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.xl,
  },
  logoText: {
    fontSize: 22,
    fontWeight: '600',
    color: GlobalStyles.gray.gray900,
    marginLeft: theme.spacing.sm,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: GlobalStyles.gray.gray900,
    marginBottom: theme.spacing.xl,
    textAlign: 'center',
  },
  formContainer: {
    width: '100%',
    gap: theme.spacing.lg,
  },
  signInContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  signInText: {
    fontSize: 14,
    color: GlobalStyles.gray.gray800,
  },
  signInLink: {
    fontSize: 14,
    color: theme.colors.brand.main,
    fontWeight: '600',
    marginLeft: 4,
  },
});
