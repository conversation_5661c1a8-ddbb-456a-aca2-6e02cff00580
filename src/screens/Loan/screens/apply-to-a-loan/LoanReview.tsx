import React, {memo} from 'react';
import {ScrollView, StyleSheet, Text, View} from 'react-native';

import MButton from '@/components/MButton';
import TxSummary from '@/components/TxSummary';
import GlobalStyles from '@/constants/GlobalStyles';
import {useAppDispatch} from '@/hooks/redux';
import {navigate, navigateViaBottomTabs} from '@/navigation/utils/navigation';
import {
  APR,
  calculateInterestByFrequency,
  formatInterestWithCurrency,
} from '@/screens/Loan/utils/loan-calculations';
import {setLoanData} from '@/storage/actions/loanActions';
import {store} from '@/storage/store';
import {Footer} from '@/styles/styled-components';
import theme from '@/styles/theme';
import {formatNumber} from '@/utils/index';

const LoanReview = ({route}) => {
  const dispatch = useAppDispatch();
  const loanData = route.params;

  const getInterestAmount = () => {
    const amount = parseFloat(loanData.borrowAmount);
    return calculateInterestByFrequency(amount, APR / 100, loanData.interestPayment);
  };

  const summaryFields = [
    {
      label: 'Withdraw',
      value: `${formatNumber(loanData.borrowAmount, {decimals: 0})} ${
        loanData.borrowCurrency.label
      }`,
    },
    {
      label: 'Loan',
      value: `${formatNumber(loanData.borrowAmount, {decimals: 0})} ${
        loanData.borrowCurrency.label
      }`,
    },
    {
      label: 'Interest / APR',
      value: `${APR}%`,
    },
    {
      label: `${loanData.interestPayment} Interest`,
      value: formatInterestWithCurrency(
        getInterestAmount(),
        loanData.borrowCurrency.label,
      ),
    },
    {
      label: 'Collateral',
      value: `${formatNumber(loanData.collateralAmount)} ${
        loanData.collateralCurrency.label
      }`,
    },
    {
      label: 'LTV',
      value: loanData.loanToValue,
    },
    {
      label: 'Term',
      value: loanData.term,
    },
  ];

  const handleProceed = () => {
    const loanDataToUse = {
      borrowAmount: loanData.borrowAmount,
      borrowCurrency: loanData.borrowCurrency.label,
      collateralAmount: loanData.collateralAmount,
      collateralCurrency: loanData.collateralCurrency.label,
      loanToValue: loanData.loanToValue,
      interestPayment: loanData.interestPayment,
      term: loanData.term,
    };

    dispatch(setLoanData(loanDataToUse));

    navigateViaBottomTabs('Loan', 'LoanKYC');
  };

  return (
    <View style={styles.root}>
      <ScrollView style={styles.content}>
        <TxSummary title="Loan Summary" fields={summaryFields} />
        <Text style={styles.disclaimer}>
          * Please note: The loan terms shown above can be adjusted after proceeding.
        </Text>
      </ScrollView>

      <Footer style={styles.footer}>
        <MButton text="Proceed with Loan" onPress={handleProceed} />
      </Footer>
    </View>
  );
};

export default memo(LoanReview);

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: 'white',
  },
  content: {
    flex: 1,
    paddingHorizontal: theme.layout.ph.screen,
    paddingVertical: theme.layout.pv.screen,
    gap: theme.layout.gap.screen,
  },
  disclaimer: {
    color: GlobalStyles.gray.gray800,
    fontSize: 14,
    paddingHorizontal: 14,
    paddingVertical: 6,
    fontStyle: 'italic',
  },
  footer: {
    marginBottom: 12,
  },
});
