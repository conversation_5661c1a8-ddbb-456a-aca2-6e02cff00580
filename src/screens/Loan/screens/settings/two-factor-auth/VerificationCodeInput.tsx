import React, {useRef} from 'react';
import {StyleSheet, TextInput, View} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';

type Props = {
  value: string;
  onChange: (val: string) => void;
  length?: number;
};

export const VerificationCodeInput: React.FC<Props> = ({value, onChange, length = 6}) => {
  const inputs = useRef<Array<TextInput | null>>([]);

  const handleChange = (text: string, idx: number) => {
    if (!/^\d*$/.test(text)) return;

    let newValue = value.split('');
    newValue[idx] = text[text.length - 1] || '';
    let joined = newValue.join('').slice(0, length);
    onChange(joined);

    if (text && idx < length - 1) {
      inputs.current[idx + 1]?.focus();
    }
  };

  const handleKeyPress = (e: any, idx: number) => {
    if (e.nativeEvent.key === 'Backspace' && !value[idx] && idx > 0) {
      inputs.current[idx - 1]?.focus();
    }
  };

  return (
    <View style={styles.container}>
      {Array.from({length}).map((_, idx) => (
        <TextInput
          key={idx}
          ref={(ref) => (inputs.current[idx] = ref)}
          style={[styles.input, value[idx] ? styles.inputActive : styles.inputInactive]}
          keyboardType="number-pad"
          maxLength={1}
          value={value[idx] || ''}
          onChangeText={(text) => handleChange(text, idx)}
          onKeyPress={(e) => handleKeyPress(e, idx)}
          autoFocus={idx === 0}
          importantForAutofill="no"
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
    marginBottom: 8,
  },
  input: {
    width: 48,
    height: 56,
    borderWidth: 2,
    borderRadius: 12,
    textAlign: 'center',
    fontSize: 20,
    fontWeight: '600',
  },
  inputActive: {
    borderColor: GlobalStyles.success.success500,
  },
  inputInactive: {
    borderColor: GlobalStyles.gray.gray600,
  },
});
