import {useState} from 'react';

export const useTwoFactorAuth = () => {
  const [code, setCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Replace with your actual QR code value (e.g., from backend)
  const qrValue = 'otpauth://totp/Assetify:username?secret=YOURSECRET&issuer=Assetify';

  // http://**************/auth/user/otp/generate
  // localhost:8001/user/otp/verify

  const onSubmit = async () => {
    setLoading(true);
    setError(null);
    try {
      // TODO: Replace with your API call for verification
      if (code.length !== 6) throw new Error('Please enter the 6-digit code.');
      // await verify2FACode(code);
      // Success: navigate or show success
    } catch (e: any) {
      setError(e.message || 'Invalid code');
    } finally {
      setLoading(false);
    }
  };

  return {
    code,
    setCode,
    loading,
    error,
    qrValue,
    onSubmit,
  };
};
