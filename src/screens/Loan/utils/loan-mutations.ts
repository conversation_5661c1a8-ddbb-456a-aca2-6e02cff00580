import {AuthenticationInstance} from '@/services/BackendServices';

type ForgotPasswordRequest = {
  email: string;
};

export const resetPassword = async ({email}: ForgotPasswordRequest) => {
  try {
    const {data} = await AuthenticationInstance.post('/user/reset-password', {
      email: email.trim().toLowerCase(),
    });
    return data;
  } catch (error: any) {
    throw new Error(error);
  }
};
