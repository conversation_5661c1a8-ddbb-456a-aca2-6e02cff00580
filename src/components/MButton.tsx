import React from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  TextStyle,
  TouchableOpacity,
  TouchableOpacityProps,
  View,
  ViewStyle,
} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';
import {Title} from '@/styles/styled-components';
import theme from '@/styles/theme';

interface Props extends TouchableOpacityProps {
  variant?: 'primary' | 'secondary';
  text: string;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  iconSpacing?: number;
  isLoading?: boolean;
  loadingText?: string;
  containerStyle?: ViewStyle;
  textStyle?: TextStyle;
}

const MButton: React.FC<Props> = ({
  variant = 'primary',
  text,
  containerStyle,
  textStyle,
  isLoading = false,
  disabled = false,
  loadingText = 'Loading',
  icon,
  iconPosition = 'left',
  iconSpacing = 8,
  ...props
}) => {
  const isPrimary = variant === 'primary';
  const isDisabled = disabled || isLoading;

  const renderContent = () => {
    if (isLoading) {
      return (
        <View style={styles.contentContainer}>
          <Title
            style={[
              styles.buttonText,
              isPrimary ? styles.primaryText : styles.secondaryText,
              textStyle,
            ]}
          >
            {loadingText}
          </Title>

          <View style={{marginLeft: 10}}>
            <ActivityIndicator
              size="small"
              color={
                isPrimary ? GlobalStyles.base.white : GlobalStyles.primary.primary400
              }
            />
          </View>
        </View>
      );
    }

    const textComponent = (
      <Title
        style={[
          styles.buttonText,
          isPrimary ? styles.primaryText : styles.secondaryText,
          isDisabled && styles.disabledText,
          textStyle,
        ]}
      >
        {text}
      </Title>
    );

    if (!icon) return textComponent;

    return (
      <View
        style={[
          styles.contentContainer,
          {flexDirection: iconPosition === 'left' ? 'row' : 'row-reverse'},
        ]}
      >
        <View
          style={{
            marginRight: iconPosition === 'left' ? iconSpacing : 0,
            marginLeft: iconPosition === 'right' ? iconSpacing : 0,
          }}
        >
          {icon}
        </View>
        {textComponent}
      </View>
    );
  };

  return (
    <TouchableOpacity
      style={[
        styles.button,
        isPrimary ? styles.primaryButton : styles.secondaryButton,
        isDisabled &&
          (isPrimary ? styles.disabledPrimaryButton : styles.disabledSecondaryButton),
        containerStyle,
      ]}
      disabled={isDisabled}
      {...props}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

export default MButton;

const styles = StyleSheet.create({
  button: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.isSmallDevice ? 14 : 16,
    borderRadius: 24,
  },
  primaryButton: {
    backgroundColor: GlobalStyles.primary.primary400,
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 0,
    borderColor: GlobalStyles.primary.primary100,
  },
  disabledPrimaryButton: {
    backgroundColor: GlobalStyles.primary.primary200,
    opacity: 0.7,
  },
  disabledSecondaryButton: {
    opacity: 0.5,
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    fontWeight: '600',
    letterSpacing: 0.3,
  },
  primaryText: {
    color: GlobalStyles.base.white,
    fontSize: 18,
  },
  secondaryText: {
    color: GlobalStyles.base.black,
    fontSize: 18,
  },
  disabledText: {
    opacity: 0.7,
  },
});
