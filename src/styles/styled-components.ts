import {Platform} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';
import styled from './index';
import theme from './theme';

/* ============================================================================================== */
/*                                             LAYOUT                                             */
/* ============================================================================================== */

export const STICKY_OFFSET = theme.isSmallDevice ? 82 : 92;

export const StickyFooter = styled.View`
  padding-horizontal: ${theme.isSmallDevice ? 32 : 48}px;
  margin-bottom: ${theme.isSmallDevice ? 18 : 26}px;
`;

export const Footer = styled.View`
  margin-vertical: ${theme.isSmallDevice ? 24 : 40}px;
  padding-horizontal: ${theme.isSmallDevice ? 28 : 32}px;
`;

/* ============================================================================================== */
/*                                              TEXT                                              */
/* ============================================================================================== */

export const Headline = styled.Text<{lineHeight?: number}>(() => ({
  fontFamily: theme.fonts.default,
  fontSize: theme.isSmallDevice ? theme.typography.lg : theme.typography.xl,
  fontWeight: '500',
  color: GlobalStyles.base.black,
  letterSpacing: -1,
  textTransform: 'uppercase',
}));

export const Subheading = styled.Text(() => ({
  fontSize: theme.typography.sm,
  color: GlobalStyles.gray.gray900,
  fontWeight: '400',
  letterSpacing: 0.4,
}));

export const Title = styled.Text<{lineHeight?: number}>(({lineHeight = 26}) => ({
  fontSize: theme.isSmallDevice ? '16px' : '18px',
  fontWeight: '500',
  color: GlobalStyles.base.black,
  letterSpacing: 0.4,
  lineHeight: `${lineHeight}px`,
}));

export const Caption = styled.Text(() => ({
  fontFamily: GlobalStyles.fonts.sfPro,
  fontSize: theme.isSmallDevice ? '20px' : '24px',
  color: GlobalStyles.base.black,
  fontWeight: '600',
  letterSpacing: 0.4,
}));

export const MenuText = styled.Text(() => ({
  fontSize: theme.isSmallDevice ? '20px' : '26px',
  color: GlobalStyles.base.black,
  letterSpacing: -1,
}));

export const BodyS = styled.Text(() => ({
  fontSize: theme.isSmallDevice ? '13px' : '15px',
  lineHeight: '20px',
  color: GlobalStyles.base.black,
  letterSpacing: Platform.OS === 'ios' ? 0.4 : undefined,
}));

export const BodySSB = styled.Text(() => ({
  fontSize: theme.isSmallDevice ? '13px' : '14px',
  lineHeight: '20px',
  color: GlobalStyles.base.black,
  letterSpacing: Platform.OS === 'ios' ? 0.4 : undefined,
}));

export const BodySB = styled.Text(() => ({
  fontSize: theme.isSmallDevice ? '13px' : '14px',
  lineHeight: '20px',
  color: GlobalStyles.base.black,
  letterSpacing: Platform.OS === 'ios' ? 0.4 : undefined,
}));

export const BodyM = styled.Text(() => ({
  fontSize: theme.isSmallDevice ? '15px' : '17px',
  lineHeight: '22px',
  color: GlobalStyles.base.black,
  letterSpacing: Platform.OS === 'ios' ? 0.4 : undefined,
}));

export const BodyMB = styled.Text(() => ({
  fontSize: '17px',
  lineHeight: '22px',
  color: GlobalStyles.base.black,
  letterSpacing: Platform.OS === 'ios' ? 0.4 : undefined,
}));

export const Text13UP = styled.Text(() => ({
  fontSize: '13px',
  lineHeight: '18px',
  textTransform: 'uppercase',
  color: GlobalStyles.base.black,
  letterSpacing: Platform.OS === 'ios' ? 0.4 : undefined,
}));

export const Footnote = styled.Text(() => ({
  fontSize: '12px',
  lineHeight: '16px',
  color: GlobalStyles.base.white,
  letterSpacing: Platform.OS === 'ios' ? 0.4 : undefined,
}));
